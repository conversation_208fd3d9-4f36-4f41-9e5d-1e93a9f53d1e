import { fileURLToPath } from 'node:url';
import { create<PERSON>iti } from 'jiti';

// Import env files to validate at build time. Use jiti so we can load .ts files in here.
await createJiti(fileURLToPath(import.meta.url)).import('./src/env');

/** @type {import('next').NextConfig} */
const config = {
  reactStrictMode: true,

  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  /** Enables hot reloading for local packages without a build step */
  transpilePackages: [
    '@willow/auth',
    '@willow/db',
    '@willow/ui',
    '@willow/utils',
  ],

  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
      },
    ];
  },

  /** We already do linting and typechecking as separate tasks in CI */
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'data.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'local-products.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'local-patients.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'local-data.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'staging-products.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'staging-patients.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'staging-data.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'production-products.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'production-patients.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'local-products-patients.startwillow.com',
      },
    ],
  },
};

export default config;
