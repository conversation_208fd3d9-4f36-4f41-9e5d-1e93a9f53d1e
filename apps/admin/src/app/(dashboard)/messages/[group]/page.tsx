'use client';

import React from 'react';
import { redirect } from 'next/navigation';

import type { DoctorAdminConversationGroups } from '~/types/doctor-admin-chat';
import { DoctorMessages } from '../_components/DoctorMessages';
import { doctorAdminConversationGroups } from '../_components/DoctorMessagesSidebar';

export default function Page({
  params,
}: {
  params: Promise<{ group: DoctorAdminConversationGroups }>;
}) {
  const { group } = React.use(params);
  if (!doctorAdminConversationGroups.includes(group))
    return redirect('/messages/myInbox');

  return <DoctorMessages group={group} />;
}
