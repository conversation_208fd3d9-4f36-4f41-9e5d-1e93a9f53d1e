import React from 'react';
import { redirect } from 'next/navigation';

import type { PatientGroup } from '@willow/db/client';
import { PatientGroupsList } from '@willow/db/client';

import { PatientsTable } from '../_components/patients-table';

export default function Page({
  params,
}: {
  params: Promise<{ group: PatientGroup }>;
}) {
  const { group } = React.use(params);
  if (!PatientGroupsList.includes(group)) return redirect('/patients/active');

  return <PatientsTable group={group} />;
}
