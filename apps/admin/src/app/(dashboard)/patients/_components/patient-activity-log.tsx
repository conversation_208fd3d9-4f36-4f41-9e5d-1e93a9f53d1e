import { useState } from 'react';

import { Loader } from '@willow/ui/loader';
import { PatientActivityLog as ActivityLog } from '@willow/ui/PatientActivityLog/index';

import {
  useCreatePatientNote,
  useGetInfinitePatientActivityLog,
} from '~/hooks/patients';

export function PatientActivityLog({ patientId }: { patientId: string }) {
  const [isHideEvents, setIsHideEvents] = useState(false);
  const handleHideEvents = () => {
    setIsHideEvents((prev) => !prev);
  };

  const {
    data,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetInfinitePatientActivityLog(patientId, {
    includes: isHideEvents
      ? 'PATIENT_NOTE_CREATED,INTERCOM_CONVERSATION_MESSAGE_CREATED'
      : undefined,
    take: 20,
  });

  const { mutateAsync: createPatientNote, isPending: isCreateNotePending } =
    useCreatePatientNote(patientId);

  if (isLoading) return <Loader className="h-full py-32" />;
  if (error) return <div>Error: {error.message}</div>;
  if (!data) return <div>No data</div>;

  return (
    <>
      <ActivityLog
        data={data.pages.flatMap((page) => page.data)}
        fetchNextPage={fetchNextPage}
        hasNextPage={hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        error={error}
        isHideEvents={isHideEvents}
        handleHideEvents={handleHideEvents}
        onCreateNote={createPatientNote}
        isCreateNotePending={isCreateNotePending}
      />
    </>
  );
}
