'use client';

import React from 'react';
import { redirect } from 'next/navigation';

import { ProductCategoriesTable } from '../_components/product-categories-table';

const ProductCategoryGroupsList = ['all'];
type ProductCategoryGroup = 'all';

export default function Page({
  params,
}: {
  params: Promise<{ group: ProductCategoryGroup }>;
}) {
  const { group } = React.use(params);
  if (!ProductCategoryGroupsList.includes(group))
    return redirect('/product-categories/all');
  return <ProductCategoriesTable />;
}
