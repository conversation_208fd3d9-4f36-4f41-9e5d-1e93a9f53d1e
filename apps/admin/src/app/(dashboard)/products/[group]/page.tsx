'use client';

import React from 'react';
import { redirect } from 'next/navigation';

import { ProductsTable } from '../_components/products-table';

const ProductGroupsList = ['all'];
type ProductGroup = 'all';

export default function Page({
  params,
}: {
  params: Promise<{ group: ProductGroup }>;
}) {
  const { group } = React.use(params);
  if (!ProductGroupsList.includes(group)) return redirect('/products/all');
  return <ProductsTable />;
}
