'use client';

import React from 'react';
import { redirect } from 'next/navigation';

import { StatesTable } from '../_components/states-table';

const StateGroupsList = ['all'];
type StateGroup = 'all';

export default function Page({
  params,
}: {
  params: Promise<{ group: StateGroup }>;
}) {
  const { group } = React.use(params);
  if (!StateGroupsList.includes(group)) return redirect('/states/all');
  return <StatesTable />;
}
