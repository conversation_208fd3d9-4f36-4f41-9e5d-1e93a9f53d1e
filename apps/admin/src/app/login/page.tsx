'use client';

import { useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowRightCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';

import banner from '~/assets/png/login-banner.png';
import DotIcon from '~/assets/svg/dot.svg';
import { useLogin } from '~/hooks/login';
import { ACCESS_TOKEN_LOCAL_STORAGE_KEY } from '~/store/accessToken';

const schema = z.object({
  email: z.string().min(2, {
    message: 'Email must be at least 2 characters.',
  }),
  password: z.string().min(2, {
    message: 'Password must be at least 2 characters.',
  }),
});

type FormType = z.infer<typeof schema>;

export default function SignInPage() {
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem(ACCESS_TOKEN_LOCAL_STORAGE_KEY);
    if (token) return router.push('/');
  }, [router]);

  const { mutateAsync: signIn, isPending } = useLogin();

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async ({ email, password }: FormType) => {
    try {
      await signIn({ email, password });
      router.push('/');
    } catch (e: any) {
      console.error(e);
      form.setError('password', { message: e.response?.data?.message });
    }
  };

  return (
    <div className="flex min-h-screen w-full">
      <div className="flex flex-1 flex-col justify-between bg-denim bg-[url('../assets/png/login-flow.png')] bg-cover md:p-12 xl:p-24">
        <div>
          <div className="mb-12 flex flex-row items-center space-x-2">
            <DotIcon />
            <h3 className="text-2xl text-white underline">Sign into Willow</h3>
          </div>
          <div className="px-7">
            <h1 className="text-5xl font-medium text-white">
              Welcome back to{' '}
            </h1>
            <h1 className="text-5xl font-medium text-electric">Willow</h1>
          </div>
        </div>
        <div className="px-7">
          <h1 className="text-4xl font-medium text-electric">
            Secure Access to Your Admin Dashboard – Keep Operations Running
            Smoothly
          </h1>
        </div>
      </div>
      <div className="flex-1 bg-white">
        <div className="h-[386px]">
          <Image
            className="h-full w-full"
            alt="Health care provider"
            src={banner}
            style={{ objectFit: 'cover' }}
          />
        </div>
        <Form {...form}>
          <div className="flex justify-center px-10 py-10">
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="w-full max-w-2xl space-y-12"
            >
              <div className="space-y-10">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <div className="relative w-full">
                        <FormControl>
                          <Input
                            variant="denim"
                            placeholder="Email"
                            {...field}
                          />
                        </FormControl>
                        <ArrowRightCircle className="absolute right-3 top-3 text-denim" />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <div className="relative w-full">
                        <FormControl>
                          <Input
                            variant="denim"
                            type="password"
                            placeholder="Password"
                            {...field}
                          />
                        </FormControl>
                        <ArrowRightCircle className="absolute right-3 top-3 text-denim" />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                size={'lg'}
                variant="denim"
                type="submit"
                className="px-12"
                loading={isPending}
              >
                LOGIN
                <ArrowRightCircle />
              </Button>
            </form>
          </div>
        </Form>
      </div>
    </div>
  );
}
