import { PrismaTransactionalClient } from '@/modules/prisma/prisma.service';
import { Prisma, PrismaClient } from '@prisma/client';

// Define a generic type for the transaction function
type TransactionFunction<T> = (tx: Prisma.TransactionClient) => Promise<T>;

export async function runInDbTransaction<T>(
  prisma: PrismaClient | PrismaTransactionalClient,
  fn: TransactionFunction<T>,
): Promise<T> {
  // If an existing transaction is passed, use it directly
  if (isTransactionClient(prisma)) {
    return fn(prisma);
  }

  // If no existing transaction, create a new one
  return (prisma as PrismaClient).$transaction(
    async (tx) => {
      return fn(tx);
    },
    { timeout: 30_000_000_000, maxWait: 60_000_000_000 },
  );
}

export type ExecutionContext = { prisma?: PrismaTransactionalClient };

export function isTransactionClient(
  client: any,
): client is PrismaTransactionalClient {
  // Check for transaction-specific methods that exist on TransactionClient
  return typeof (client as PrismaClient).$transaction !== 'function';
}
