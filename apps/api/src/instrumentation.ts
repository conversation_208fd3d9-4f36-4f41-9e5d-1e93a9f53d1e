import { registerInstrumentations } from '@opentelemetry/instrumentation';
import { HttpInstrumentation } from '@opentelemetry/instrumentation-http';
import { NestInstrumentation } from '@opentelemetry/instrumentation-nestjs-core';
import tracer from 'dd-trace';

tracer.init({
  service: 'api',
  env: process.env.ENVIRONMENT || 'development',
  version: process.env['API_VERSION'] || '0.0.0',
  // logInjection: true,
  sampleRate: 1,
  profiling: true,
});

const { TracerProvider } = tracer;

const provider = new TracerProvider();
provider.register();

registerInstrumentations({
  instrumentations: [new HttpInstrumentation(), new NestInstrumentation()],
  tracerProvider: provider,
});
