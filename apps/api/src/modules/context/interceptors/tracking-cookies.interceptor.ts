import type {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  NestInterceptor,
} from '@nestjs/common';
import type { Request } from 'express';
import type { Observable } from 'rxjs';
import { Injectable } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';

import { TRACKING_AFFILIATE_COOKIE_NAME } from '@willow/utils/tracking';

import { getClientIp } from '../context.helpers';
import { Tracking } from '../context.service';

/**
 * Interceptor that populates the CLS context with request-specific data.
 * This runs after guards but before route handlers, ensuring context is available
 * throughout the request lifecycle.
 */
@Injectable()
export class TrackingCookiesInterceptor implements NestInterceptor {
  constructor(private readonly cls: ClsService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (context.getType() === 'http') {
      const req = context.switchToHttp().getRequest<Request>();

      const ip = getClientIp(req);
      const segmentAnonymousId = req.cookies.ajs_anonymous_id as
        | string
        | undefined;

      const utmTracking: { [key: string]: unknown } = {};
      utmTracking.campaign = parseUtmFromCookies(req, 'l_');
      utmTracking.firstCampaign = parseUtmFromCookies(req, 'ft_');

      if (req.cookies[TRACKING_AFFILIATE_COOKIE_NAME]) {
        utmTracking.referrer = JSON.parse(
          req.cookies[TRACKING_AFFILIATE_COOKIE_NAME] as string,
        ) as unknown;
      }

      let facebookTracking: Tracking['facebook'] = undefined;
      if (req.cookies._fbc || req.cookies._fbp) {
        facebookTracking = {
          fbc: req.cookies._fbc as string | undefined,
          fbp: req.cookies._fbp as string | undefined,
        };
      }

      this.cls.set('tracking', {
        ip,
        segmentAnonymousId,
        utm: utmTracking,
        facebook: facebookTracking,
      } satisfies Tracking);
    }

    return next.handle();
  }
}

const parseUtmFromCookies = (req: Request, prefix: string) => {
  const utm: { [key: string]: string } = {};
  const mapping: { [key: string]: string } = {
    utm_source: 'source',
    utm_medium: 'medium',
    utm_campaign: 'name',
    utm_term: 'term',
    utm_content: 'content',
  };

  Object.entries(mapping).forEach(([param, key]) => {
    const cookieName = `${prefix}${param}`;
    if (req.cookies[cookieName]) {
      utm[key] = req.cookies[cookieName];
    }
  });

  return Object.keys(utm).length > 0 ? utm : undefined;
};
