import { runInDbTransaction } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import { DoctorProfile } from '@adapters/persistence/database/doctor.persistence';
import { MedicalNecessityService } from '@modules/onboarding/services/medical-necessity.service';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class UpdatePatientMedicalNecessitiesUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly medicalNecessityService: MedicalNecessityService,
    private readonly eventEmitter: EventEmitter2,
    private readonly segment: SegmentService,
  ) {}

  async execute(
    patientId: string,
    necessityKeys: string[],
    doctor: DoctorProfile,
  ) {
    return runInDbTransaction(this.prismaService, async (prisma) => {
      // Verify the patient belongs to this doctor
      await prisma.patient.findUniqueOrThrow({
        where: { id: patientId, doctorId: doctor.id },
        select: { id: true },
      });

      // Update medical necessities
      await this.medicalNecessityService.updatePatientMedicalNecessities(
        patientId,
        necessityKeys,
        prisma,
      );

      // Fetch and return the updated medical necessities with text
      const updatedNecessities = await prisma.medicalNecessity.findMany({
        where: { patientId },
        select: {
          id: true,
          necessity: true,
          setBy: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: { createdAt: 'desc' },
      });

      // Transform to include human-readable text
      const result = updatedNecessities.map((mn) => ({
        ...mn,
        text:
          this.medicalNecessityService.getMedicalNecessityText(mn.necessity) ||
          mn.necessity,
      }));

      // Track medical necessity update event
      const necessityTexts = result.map((mn) => mn.text);
      await this.segment.track(
        patientId,
        'medicalNecessityUpdated',
        {
          properties: {
            Type: 'Doctor',
            User: `${doctor.user.firstName} ${doctor.user.lastName}`,
            Reason: necessityTexts,
          },
        },
        { prisma },
      );

      return result;
    });
  }
}
