import { PrismaService } from '@/modules/prisma/prisma.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import {
  PgTopicConsumer,
  PgTopicMessage,
} from '@/modules/shared/queue/pg-topic-consumer.abstract';
import { TreatmentService } from '@/modules/treatment/services/treatment.service';
import { TreatmentMachineContext } from '@/modules/treatment/states/treatment.state';
import {
  TreatmentUpdatedTopic,
  TreatmentUpdatedTopicEvent,
  TreatmentUpdatedTopicPayload,
} from '@/modules/treatment/topics/treatment-updated.topic';
import { Injectable } from '@nestjs/common';

import { FollowUpService } from '../services/follow-up.service';
import { CancelPatientFollowUpUseCase } from '../use-cases/cancel-patient-follow-up.use-case';

@Injectable()
export class TreatmentUpdatedCancelFollowUpConsumer extends PgTopicConsumer<
  TreatmentUpdatedTopicPayload,
  TreatmentUpdatedTopicEvent
> {
  private logger: LoggerService;

  constructor(
    private readonly prisma: PrismaService,
    private readonly treatmentService: TreatmentService,
    private readonly followUpService: FollowUpService,
    private readonly cancelPatientFollowUp: CancelPatientFollowUpUseCase,
    loggerFactory: LoggerFactory,
    treatmentUpdatedTopic: TreatmentUpdatedTopic,
  ) {
    super(treatmentUpdatedTopic, {
      consumerName: 'treatment-updated-cancel-follow-up.consumer',
      events: ['cancelled'],
    });
    this.logger = loggerFactory.createLogger(
      TreatmentUpdatedCancelFollowUpConsumer.name,
    );
  }

  async onMessage({
    payload,
  }: PgTopicMessage<TreatmentUpdatedTopicPayload, TreatmentUpdatedTopicEvent>) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    const { patientId, treatmentId } = context;

    await this.prisma.$transaction(async (prisma) => {
      const isCore = await this.treatmentService.isCore(treatmentId, {
        prisma,
      });
      if (!isCore)
        return {
          success: false,
          message: 'Treatment is not core',
        };

      const followUp = await this.followUpService.getActive(patientId, {
        prisma,
      });
      if (!followUp)
        return {
          success: false,
          message: 'No active follow-up found',
        };

      if (followUp.treatmentId !== treatmentId)
        return {
          success: false,
          message: 'Follow-up found is not for the canceled treatment',
          followUpId: followUp.id,
        };
      const canceledFollowUp = await this.cancelPatientFollowUp.execute(
        followUp.id,
        { prisma },
      );
      return {
        success: true,
        message: 'Follow-up canceled successfully',
        followUpId: canceledFollowUp.id,
      };
    });
  }
}
