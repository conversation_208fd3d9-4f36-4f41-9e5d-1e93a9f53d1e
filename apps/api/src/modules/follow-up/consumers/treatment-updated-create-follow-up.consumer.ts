import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import {
  PgTopicConsumer,
  PgTopicMessage,
} from '@/modules/shared/queue/pg-topic-consumer.abstract';
import { TreatmentMachineContext } from '@/modules/treatment/states/treatment.state';
import {
  TreatmentUpdatedTopic,
  TreatmentUpdatedTopicEvent,
  TreatmentUpdatedTopicPayload,
} from '@/modules/treatment/topics/treatment-updated.topic';
import { Injectable } from '@nestjs/common';
import { subDays } from 'date-fns';

import { FollowUpService } from '../services/follow-up.service';
import { CancelPatientFollowUpUseCase } from '../use-cases/cancel-patient-follow-up.use-case';

@Injectable()
export class TreatmentUpdatedCreateFollowUpConsumer extends PgTopicConsumer<
  TreatmentUpdatedTopicPayload,
  TreatmentUpdatedTopicEvent
> {
  private logger: LoggerService;

  constructor(
    private readonly followUpService: FollowUpService,
    private readonly cancelPatientFollowUp: CancelPatientFollowUpUseCase,
    loggerFactory: LoggerFactory,
    treatmentUpdatedTopic: TreatmentUpdatedTopic,
  ) {
    super(treatmentUpdatedTopic, {
      consumerName: 'treatment-updated-create-follow-up.consumer',
      // TODO: change this to wait for treatment prescribed
      events: ['created'],
    });
    this.logger = loggerFactory.createLogger(
      TreatmentUpdatedCreateFollowUpConsumer.name,
    );
  }

  async onMessage({
    payload,
  }: PgTopicMessage<TreatmentUpdatedTopicPayload, TreatmentUpdatedTopicEvent>) {
    const treatementState = payload.treatment.state as {
      context: TreatmentMachineContext;
    };

    const { context: treatmentContext } = treatementState;
    // 1. check if treatment is core

    const { patientId, treatmentId, endOfLastRefillDate } = treatmentContext;

    const isCore = treatmentContext.isCore;
    if (!isCore)
      return {
        success: false,
        message: 'Treatment is not core',
      };

    // 2. get current active follow up for patient, if exists
    const existing = await this.followUpService.getActive(patientId);
    if (existing) {
      await this.cancelPatientFollowUp.execute(existing.id);
    }

    const scheduledAt = subDays(endOfLastRefillDate, 7);
    const createdFollowUp = await this.followUpService.create(
      patientId,
      treatmentId,
      scheduledAt,
    );
    return {
      success: true,
      message: 'Follow up created successfully',
      patientId,
      followUpId: createdFollowUp.id,
      scheduledAt,
    };
  }
}
