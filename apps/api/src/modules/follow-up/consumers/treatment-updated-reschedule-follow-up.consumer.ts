import { PrismaService } from '@/modules/prisma/prisma.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import {
  PgTopicConsumer,
  PgTopicMessage,
} from '@/modules/shared/queue/pg-topic-consumer.abstract';
import { TreatmentService } from '@/modules/treatment/services/treatment.service';
import { TreatmentMachineContext } from '@/modules/treatment/states/treatment.state';
import {
  TreatmentUpdatedTopic,
  TreatmentUpdatedTopicEvent,
  TreatmentUpdatedTopicPayload,
} from '@/modules/treatment/topics/treatment-updated.topic';
import { Injectable } from '@nestjs/common';
import { subDays } from 'date-fns';

import { FollowUpService } from '../services/follow-up.service';

@Injectable()
export class TreatmentUpdatedRescheduleFollowUpConsumer extends PgTopicConsumer<
  TreatmentUpdatedTopicPayload,
  TreatmentUpdatedTopicEvent
> {
  private logger: LoggerService;

  constructor(
    private readonly prisma: PrismaService,
    private readonly treatmentService: TreatmentService,
    private readonly followUpService: FollowUpService,
    loggerFactory: LoggerFactory,
    treatmentUpdatedTopic: TreatmentUpdatedTopic,
  ) {
    super(treatmentUpdatedTopic, {
      consumerName: 'treatment-updated-reschedule-follow-up.consumer',
      events: ['end_date_changed', 'resumed'],
    });
    this.logger = loggerFactory.createLogger(
      TreatmentUpdatedRescheduleFollowUpConsumer.name,
    );
  }

  async onMessage({
    payload,
  }: PgTopicMessage<TreatmentUpdatedTopicPayload, TreatmentUpdatedTopicEvent>) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    const { endOfLastRefillDate, patientId, treatmentId } = context;

    await this.prisma.$transaction(async (prisma) => {
      const isCore = await this.treatmentService.isCore(treatmentId, {
        prisma,
      });

      if (!isCore)
        return {
          success: false,
          message: 'Treatment is not core',
        };

      // check if there is a scheduled follow up for this patient
      const followUp = await this.followUpService.getActive(patientId, {
        prisma,
      });

      if (!followUp)
        return {
          success: false,
          message: 'No active follow-up found',
        };

      // check if the follow up is for the cancelled treatment
      if (followUp.treatmentId !== treatmentId) return;

      // check if the follow up is already scheduled at the new end of last refill date
      const scheduledAt = subDays(new Date(endOfLastRefillDate), 14);
      if (
        followUp.scheduledAt.getTime() ===
        new Date(endOfLastRefillDate).getTime()
      ) {
        return {
          success: false,
          message:
            'Follow up is already scheduled at the new end of last refill date',
        };
      }

      // shift the scheduled follow up
      const updatedFollowUp = await this.followUpService.moveScheduledAt(
        followUp.id,
        scheduledAt,
        {
          prisma,
        },
      );

      return {
        success: true,
        message: 'Follow up rescheduled successfully',
        scheduledAt,
        followUpId: updatedFollowUp.id,
      };
    });
  }
}
