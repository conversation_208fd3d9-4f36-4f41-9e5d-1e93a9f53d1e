import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { OnboardingUpdatedEvent } from '@modules/onboarding/events/onboarding-updated.event';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Request } from 'express';

@Injectable()
export class PatientOnboardingService {
  constructor(private readonly patientPersistence: PatientPersistence) {}

  async getPatientData(req: Request): Promise<PatientOnboardingProfile> {
    const cognitoId = req.user['userId'];
    if (!cognitoId) throw new Error('Missing cognitoId in request');

    const patient =
      await this.patientPersistence.getOnboardingProfile(cognitoId);

    if (!patient)
      throw new Error(`Patient not found for username: ${cognitoId}`);

    return patient;
  }
  // @todo might want to update the cached data and cookie, but for now we'll re-fetch it
  // async updateOnboardingSession(req: Request, data: Partial<PatientProfile>) {}

  @OnEvent('onboarding.updated')
  async updateOnboardingPatientData(event: OnboardingUpdatedEvent) {
    return this.patientPersistence.update(event.patientId, event.data);
  }
}
