import { PatientService } from '@modules/patient/patient.service';
import { Injectable } from '@nestjs/common';

import { MergeDuplicateAccountDto } from '../dto/merge-duplicate-account.dto';

@Injectable()
export class AdminMergePatientDuplicateAccountUseCase {
  constructor(private readonly patientService: PatientService) {}

  async execute(adminId: string, data: MergeDuplicateAccountDto) {
    const abandonedEmail = data.newEmail.replace(/(@)/, '.abandoned$1');

    await this.patientService.updateEmail(
      data.newEmail,
      abandonedEmail,
      adminId,
    );

    try {
      await this.patientService.updateEmail(data.email, data.newEmail, adminId);
    } catch (error) {
      console.error('Error updating patient email', error);
      // Rollback
      await this.patientService.updateEmail(
        abandonedEmail,
        data.newEmail,
        adminId,
      );
      throw error;
    }

    try {
      const abandonedProfile =
        await this.patientService.getPatientDataByEmail(abandonedEmail);

      await this.patientService.delete(abandonedProfile.user.id);
    } catch (e) {
      console.error(`Error deleting patient: ${abandonedEmail}`, e);
    }

    return {
      success: true,
      message: `Patient email updated from: ${data.email} to ${data.newEmail}`,
    };
  }
}
