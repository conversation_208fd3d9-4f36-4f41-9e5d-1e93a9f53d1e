import { EmailTopLvlDomain } from '@modules/shared/validators/top-level-domain';
import { Transform } from 'class-transformer';
import { IsEmail, Validate } from 'class-validator';

export class MergeDuplicateAccountDto {
  @IsEmail()
  @Transform(({ value }) => value.toLowerCase())
  @Validate(EmailTopLvlDomain)
  newEmail: string;

  @IsEmail()
  @Transform(({ value }) => value.toLowerCase())
  @Validate(EmailTopLvlDomain)
  email: string;
}
