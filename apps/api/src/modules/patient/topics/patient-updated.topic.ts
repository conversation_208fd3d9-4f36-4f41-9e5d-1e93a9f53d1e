// import { PgTopic } from '@/modules/shared/queue/pg-topic.abstract';
// import { Injectable } from '@nestjs/common';
// import { Patient } from '@prisma/client';

// export const PatientUpdatedTopicEventList = ['created']
// export type PatientUpdatedTopicPayload = { patient: Patient };
// export type

// @Injectable()
// export class PatientUpdatedTopic extends PgTopic<PatientUpdatedTopicPayload> {
//   constructor(private readonly eventEmitter: EventEmitter2) {}

//   publish(patientId: string) {
//     this.eventEmitter.emit('patient.updated', { patientId });
//   }
// }
