import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Analytics } from '@segment/analytics-node';

import { SegmentIdentify, SegmentTrack } from './segment.definitions';

@Injectable()
export class SegmentAdapter {
  private analytic;

  constructor(private readonly configService: ConfigService) {
    this.analytic = new Analytics({
      writeKey: String(this.configService.getOrThrow('SEGMENT_WRITE_KEY')),
    });
  }

  async track(data: SegmentTrack) {
    return new Promise<void>((resolve, reject) => {
      this.analytic.track(data, (err) => {
        if (err) {
          reject(err);
        }
        resolve();
      });
    });
  }

  async identify(data: SegmentIdentify) {
    return new Promise<void>((resolve, reject) => {
      this.analytic.identify(data, (err) => {
        if (err) {
          reject(err);
        }

        resolve();
      });
    });
  }
}
