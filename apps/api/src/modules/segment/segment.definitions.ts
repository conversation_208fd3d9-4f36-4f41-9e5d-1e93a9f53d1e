import type { CampaignData } from '@willow/utils/tracking';

// export type StoredCampaignData = {
//   campaign: CampaignData;
//   firstCampaign: CampaignData;
//   fbp: headerType;
//   fbc: headerType;
//   userAgent: headerType;
//   ip: headerType;
// };

type SegmentEvent<T extends object> = {
  anonymousId?: string;
  userId: string;
  context?: {
    app?: {
      name?: string;
      version?: string;
      build?: string;
    };
    ip?: string;
    userAgent?: string;
    campaign?: CampaignData;
    referrer?: Record<string, unknown>;
  };
  // integrations?: Integrations | undefined;
} & T;

export type SegmentIdentify = SegmentEvent<{
  traits?: unknown;
}>;

export type SegmentTrack = SegmentEvent<{
  event: string;
  properties?: Record<string, unknown>;
}>;

export type SegmentTrackEventDefinition<TEventName, TProperties> = {
  event: TEventName;
  properties: TProperties;
};

export type SegmentTrackEvent =
  | SegmentTrackEventDefinition<'RefundIssued', Record<string, any>>
  | SegmentTrackEventDefinition<'LeadGenerated', Record<string, any>>
  | SegmentTrackEventDefinition<'OnboardingStarted', Record<string, any>>
  | SegmentTrackEventDefinition<'StateSelected', Record<string, any>>
  | SegmentTrackEventDefinition<'OnboardingReject', Record<string, any>>
  | SegmentTrackEventDefinition<'OnboardingComplete', Record<string, any>>
  | SegmentTrackEventDefinition<'WaitlistJoined', Record<string, any>>
  | SegmentTrackEventDefinition<'AccountCreated', Record<string, any>>
  | SegmentTrackEventDefinition<'SignUp', Record<string, any>>
  | SegmentTrackEventDefinition<'VisitStarted', Record<string, any>>
  | SegmentTrackEventDefinition<'QuestionnaireCompleted', Record<string, any>>
  | SegmentTrackEventDefinition<
      'necessityStatementComplete',
      Record<string, any>
    >
  | SegmentTrackEventDefinition<'TreatmentChosen', Record<string, any>>
  | SegmentTrackEventDefinition<
      'IdentityVerificationStarted',
      Record<string, any>
    >
  | SegmentTrackEventDefinition<'PhotoUploaded', Record<string, any>>
  | SegmentTrackEventDefinition<'PhotoUploadSkipped', Record<string, any>>
  | SegmentTrackEventDefinition<'IDUploaded', Record<string, any>>
  | SegmentTrackEventDefinition<'IDUploadSkipped', Record<string, any>>
  | SegmentTrackEventDefinition<
      'IdentityVerificationComplete',
      Record<string, any>
    >
  | SegmentTrackEventDefinition<'VisitSummary', Record<string, any>>
  | SegmentTrackEventDefinition<'CheckoutStarted', Record<string, any>>
  | SegmentTrackEventDefinition<'CheckoutComplete', Record<string, any>>
  | SegmentTrackEventDefinition<'PatientAccepted', Record<string, any>>
  | SegmentTrackEventDefinition<
      'PatientIdentityVerificationStarted',
      Record<string, any>
    >
  | SegmentTrackEventDefinition<'IdentityAccepted', Record<string, any>>
  | SegmentTrackEventDefinition<'IdentityRejected', Record<string, any>>
  | SegmentTrackEventDefinition<'MedicationPrescribed', Record<string, any>>
  | SegmentTrackEventDefinition<'patientCancelled', Record<string, any>>
  | SegmentTrackEventDefinition<'patientUncancelled', Record<string, any>>
  | SegmentTrackEventDefinition<'DoctorSentMessage', Record<string, any>>
  | SegmentTrackEventDefinition<'PatientSentMessage', Record<string, any>>
  | SegmentTrackEventDefinition<'BillingUpdated', Record<string, any>>
  | SegmentTrackEventDefinition<'ShippingUpdated', Record<string, any>>
  | SegmentTrackEventDefinition<'ProfileUpdated', Record<string, any>>
  | SegmentTrackEventDefinition<'EmailUpdated', Record<string, any>>
  | SegmentTrackEventDefinition<'paymentProcessed', Record<string, any>>
  | SegmentTrackEventDefinition<'productPurchase', Record<string, any>>
  | SegmentTrackEventDefinition<'paymentFailed', Record<string, any>>
  | SegmentTrackEventDefinition<'dosageNotification', Record<string, any>>
  | SegmentTrackEventDefinition<'refillNotification', Record<string, any>>
  | SegmentTrackEventDefinition<'subscriptionCancelled', Record<string, any>>
  | SegmentTrackEventDefinition<'invoiceUncollectible', Record<string, any>>
  | SegmentTrackEventDefinition<'treatmentTypeChosen', Record<string, any>>
  | SegmentTrackEventDefinition<'followUpSent', Record<string, any>>
  | SegmentTrackEventDefinition<'followUpStarted', Record<string, any>>
  | SegmentTrackEventDefinition<'followUpComplete', Record<string, any>>
  | SegmentTrackEventDefinition<'followUpCancelled', Record<string, any>>
  | SegmentTrackEventDefinition<'doctorCompleteFollowUp', Record<string, any>>
  | SegmentTrackEventDefinition<'NPS Survey Response', Record<string, any>>
  | SegmentTrackEventDefinition<'patientReassigned', Record<string, any>>
  | SegmentTrackEventDefinition<'PharmacyMigrated', Record<string, any>>
  | SegmentTrackEventDefinition<'orderSent', Record<string, any>>
  | SegmentTrackEventDefinition<'referralReceived', Record<string, any>>
  | SegmentTrackEventDefinition<'referralSent', Record<string, any>>
  | SegmentTrackEventDefinition<
      'referralLinkAccountCreated',
      Record<string, any>
    >
  | SegmentTrackEventDefinition<
      'referralLinkCheckoutComplete',
      Record<string, any>
    >
  | SegmentTrackEventDefinition<'disputeFiled', Record<string, any>>
  | SegmentTrackEventDefinition<'MessageRouted', Record<string, any>>
  | SegmentTrackEventDefinition<'PrescriptionTransferred', Record<string, any>>
  | SegmentTrackEventDefinition<'PrescriptionMigrated', Record<string, any>>
  | SegmentTrackEventDefinition<
      'prescriptionUncollectible',
      Record<string, any>
    >
  | SegmentTrackEventDefinition<'AdminMessagedDoctor', Record<string, any>>
  | SegmentTrackEventDefinition<'DoctorMessagedAdmin', Record<string, any>>
  | SegmentTrackEventDefinition<
      'VerificationServiceSubmit',
      Record<string, any>
    >
  | SegmentTrackEventDefinition<
      'VerificationServiceAccept',
      Record<string, any>
    >
  | SegmentTrackEventDefinition<
      'VerificationServiceReject',
      Record<string, any>
    >
  | SegmentTrackEventDefinition<'medicalNecessityUpdated', Record<string, any>>
  | SegmentTrackEventDefinition<'patientDeleted', Record<string, any>>;

export type SegmentTrackEventName = SegmentTrackEvent['event'];
