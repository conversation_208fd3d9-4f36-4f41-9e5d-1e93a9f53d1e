import { ExecutionContext } from '@/helpers/transaction';
import {
  Inject,
  Injectable,
  OnApplicationBootstrap,
  OnModuleDestroy,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import PgBoss from 'pg-boss';

import PgBossClient from './pgboss.provider';

export type Job<TPayload extends object> = PgBoss.Job<TPayload>;

export type PgQueueSendOptions = Pick<ExecutionContext, 'prisma'> & {
  priority?: number;
  startAfter?: Date;
  singletonPolicy?: 'throttle' | 'debounce';
  singletonKey?: string;
  singletonIntervalInSeconds?: number;
  source?: string;
};

export class PgBossError extends Error {
  constructor(message: string, cause?: Error) {
    super(message);
    this.name = 'PgBossError';
    if (cause) {
      this.cause = cause;
    }
  }
}

export class PgQueueJobFailedError extends PgBossError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(
      JSON.stringify({
        message,
        ...details,
      }),
    );
    this.name = 'PgQueueJobFailedError';
  }
}

type PgQueuePolicy = 'standard' | 'one-active-per-singleton-key';
type PgQueueWorkerOptions = {
  concurrency: number;
  pollingIntervalSeconds: number;
};
export type PgQueueOptions = Partial<
  PgQueueWorkerOptions &
    Omit<PgBoss.Queue, 'name' | 'policy'> & { policy: PgQueuePolicy }
>;

const mapPolicyToPgBoss = (policy: PgQueuePolicy): PgBoss.Queue['policy'] => {
  if (policy === 'one-active-per-singleton-key') return 'singleton';
  else return 'standard';
};

@Injectable()
export abstract class PgQueueBase<TPayload extends object>
  implements OnApplicationBootstrap, OnModuleDestroy
{
  @Inject()
  protected readonly pgbossClient!: PgBossClient;

  @Inject()
  protected readonly config!: ConfigService;

  public readonly queueName: string;
  protected readonly options: PgQueueWorkerOptions & Omit<PgBoss.Queue, 'name'>;

  protected abstract _work(job: Job<TPayload>): Promise<unknown>;

  private workerHandlers: string[] = [];

  public isDisabled = false;

  constructor(jobName: string, options: PgQueueOptions = {}) {
    this.queueName = jobName;
    this.options = {
      ...options,
      concurrency: options.concurrency ?? 1,
      pollingIntervalSeconds: options.pollingIntervalSeconds ?? 5,
      policy: mapPolicyToPgBoss(options.policy ?? 'standard'),
      // deleteAfterSeconds: options.deleteAfterSeconds ?? 60 * 86400, // 60 days
      retentionSeconds: options.retentionSeconds ?? 60 * 86400, // 60 days
    };
  }

  async onApplicationBootstrap() {
    console.log('Creating PgBoss queue:', this.queueName, this.options);
    await this.pgbossClient.ensureQueue(this.queueName, {
      name: this.queueName,
      ...this.options,
    });

    this.isDisabled =
      this.config.get('IS_CLI') === 'true' ||
      this.config.get('ENABLE_PGBOSS_CONSUMER') !== 'true';
    if (this.isDisabled) {
      console.warn('Pg Boss is disabled, skipping worker initialization');
      return;
    }

    for (let i = 0; i < this.options.concurrency; i++) {
      const handler = await this.pgbossClient.pgboss.work<TPayload>(
        this.queueName,
        { pollingIntervalSeconds: this.options.pollingIntervalSeconds },
        async (jobs) => {
          for (const job of jobs) {
            return this._work(job);
          }
        },
      );
      this.workerHandlers.push(handler);
    }
  }

  async onModuleDestroy() {
    if (this.isDisabled) return;
    await this.pgbossClient.pgboss.offWork(this.queueName);
  }

  async _send(
    payload: TPayload,
    {
      prisma,
      singletonKey,
      singletonPolicy,
      singletonIntervalInSeconds,
      ...options
    }: PgQueueSendOptions = {},
  ): Promise<{ jobId: string }> {
    try {
      const jobId = await this.pgbossClient.pgboss.send(
        this.queueName,
        payload,
        {
          ...options,
          singletonKey: singletonKey,
          singletonSeconds: singletonIntervalInSeconds,
          singletonNextSlot: singletonPolicy === 'debounce' ? true : undefined,
          db: prisma ? this.pgbossClient.mapPrismaToPgBoss(prisma) : undefined,
        },
      );
      if (!jobId && !singletonPolicy) {
        throw new PgBossError('Failed to send job to PgBoss queue');
      }
      return { jobId };
    } catch (error) {
      console.error('[PgBoss]', error);
      throw new PgBossError(
        'Failed to send job to PgBoss queue',
        error instanceof PgBossError ? error : undefined,
      );
    }
  }
}

type JobMessage<
  TPayload extends object,
  TMetadata extends object = {
    queue: string;
    patientId: string;
    source: string | null;
  },
> = {
  metadata: TMetadata;
  payload: TPayload;
};
export type JobMetadata<
  TPayload extends object = object,
  TMetadata extends object = {
    queue: string;
    patientId: string;
    source: string | null;
  },
> = JobMessage<TPayload, TMetadata>['metadata'] & {
  pgboss: Pick<Job<TPayload>, 'expireInSeconds' | 'id' | 'name'>;
};

@Injectable()
export abstract class PgQueue<TPayload extends object> extends PgQueueBase<
  JobMessage<TPayload>
> {
  protected abstract work(
    job: TPayload,
    metadata: JobMetadata,
  ): Promise<unknown>;

  public async _work(job: Job<JobMessage<TPayload>>): Promise<unknown> {
    return this.work(job.data.payload, {
      ...job.data.metadata,
      pgboss: {
        expireInSeconds: job.expireInSeconds,
        id: job.id,
        name: job.name,
      },
    });
  }

  public async send(
    patientId: string,
    payload: TPayload,
    { source, ...options }: PgQueueSendOptions = {},
  ): Promise<{ jobId: string }> {
    return super._send(
      {
        metadata: {
          queue: this.queueName,
          patientId,
          source: source ?? null,
        },
        payload,
      },
      options,
    );
  }
}
