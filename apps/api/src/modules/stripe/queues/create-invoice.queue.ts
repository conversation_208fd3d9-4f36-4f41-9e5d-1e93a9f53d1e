import { ExecutionContext, runInDbTransaction } from '@/helpers/transaction';
import {
  PrismaService,
  PrismaTransactionalClient,
} from '@/modules/prisma/prisma.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import {
  PgQueue,
  PgQueueJobFailedError,
} from '@/modules/shared/queue/pg-queue.abstract';
import { Injectable } from '@nestjs/common';
import { Prescription, Treatment } from '@prisma/client';
import { differenceInDays } from 'date-fns';

import { StripeService } from '../service/stripe.service';

type NewInvoicePayload = { patientId: string; prescriptionIds: string[] };
export type CreateInvoicePayload = NewInvoicePayload & {
  options: {
    skipSanityCheck: boolean;
    skipAssertOnlyUniqueNewRefillsCheck: boolean;
    skipAssertInvoiceDoesntExistInStripeCheck: boolean;
    maxOldPrescriptionDays: number;
  };
};

type TPatient = {
  id: string;
  status: string;
  stripeCustomerId: string | null;
};

type TPrescription = {
  id: string;
  treatmentId: string;
  refill: number;
  createdAt: Date;
  status: Prescription['status'];
  stripeInvoiceId: string | null;
  stripeCouponId: string | null;
  productPriceId: string;
  treatment: {
    id: string;
    vials: number;
    status: Treatment['status'];
  };
};

@Injectable()
export class CreateInvoiceQueue extends PgQueue<CreateInvoicePayload> {
  private readonly logger: LoggerService;

  constructor(
    private readonly prisma: PrismaService,
    private readonly stripe: StripeService,
    private readonly loggerFactory: LoggerFactory,
  ) {
    super('stripe-create-invoice.queue', {
      policy: 'one-active-per-singleton-key',
      concurrency: 5,
      pollingIntervalSeconds: 0.5,
      retryLimit: 0,
      retryDelay: 60,
    });
    this.logger = this.loggerFactory.createLogger(CreateInvoiceQueue.name);
  }

  public async addToQueue(
    patientId: string,
    prescriptionIds: string[],
    opts?: ExecutionContext,
  ) {
    return this.send(
      patientId,
      {
        patientId,
        prescriptionIds,
        options: {
          skipSanityCheck: false,
          skipAssertOnlyUniqueNewRefillsCheck: false,
          skipAssertInvoiceDoesntExistInStripeCheck: false,
          maxOldPrescriptionDays: 3,
        },
      },
      {
        singletonKey: `pi_${patientId}`,
        singletonIntervalInSeconds: 60 * 5,
        singletonPolicy: 'throttle',
        prisma: opts?.prisma,
      },
    );
  }

  async work({ options, patientId, prescriptionIds }: CreateInvoicePayload) {
    const result = await this.assertPatientIsGood(patientId);
    if (result.success === false) {
      await this.voidPrescriptions(prescriptionIds);
      return { success: false, messages: 'patient is not active', patientId };
    }

    const patient = result.patient;

    const checksResults = await runInDbTransaction(this.prisma, async (tx) => {
      const prescriptions: TPrescription[] = await tx.prescription.findMany({
        where: { id: { in: prescriptionIds } },
        select: {
          id: true,
          treatmentId: true,
          refill: true,
          createdAt: true,
          status: true,
          stripeInvoiceId: true,
          stripeCouponId: true,
          productPriceId: true,
          treatment: {
            select: { id: true, status: true, vials: true },
          },
        },
      });

      // All prescription exist
      if (prescriptions.length !== prescriptionIds.length) {
        throw new PgQueueJobFailedError(`Some prescriptions do not exist`, {
          patientId: patientId,
          prescriptionIds: prescriptionIds,
        });
      }

      const treatmentsCheck = await this.assertTreatmentsAreActive(
        patientId,
        prescriptions,
      );

      if (treatmentsCheck.success === false) {
        await this.voidPrescriptions(prescriptionIds, tx);
        return {
          success: false,
          messages: 'treatments are cancelled',
        };
      }

      if (options.skipSanityCheck !== true) {
        await this.assertSanityCheck(tx, patientId, prescriptions, {
          maxOldPrescriptionDays: options.maxOldPrescriptionDays,
        });
      }

      if (options.skipAssertOnlyUniqueNewRefillsCheck !== true) {
        await this.assertOnlyUniqueNewRefillsCheck(
          tx,
          patientId,
          prescriptions,
        );
      }

      return {
        success: true,
        prescriptions,
      };
    });

    if (checksResults.success == false) {
      return checksResults;
    }

    const prescriptions = checksResults.prescriptions;

    // costly check: run it outside of the transaction
    await this.assertInvoiceDoesntExistInStripeCheck(patientId, prescriptions);

    // create invoice in stripe
    return this.createInvoice(patient, prescriptions);
  }

  private async assertPatientIsGood(patientId: string): Promise<
    | {
        success: boolean;
        patient: {
          id: string;
          status: string;
          stripeCustomerId: string | null;
        };
      }
    | {
        success: false;
        error: string;
      }
  > {
    const patient = await this.prisma.patient.findUnique({
      where: { id: patientId },
      select: { id: true, status: true, stripeCustomerId: true },
    });

    if (!patient) {
      throw new PgQueueJobFailedError(`Patient ${patientId} does not exist`, {
        patientId: patientId,
      });
    }

    const INACTIVE_STATUSES = [
      'banned',
      'cancelled',
      'deleted',
      'onboardingRejected',
      'pendingApprovalFromDoctor',
    ];

    if (INACTIVE_STATUSES.includes(patient.status)) {
      return {
        success: false,
        error: `Patient ${patientId} is not active`,
      };
    }

    return {
      success: true,
      patient,
    };
  }

  private async assertTreatmentsAreActive(
    patientId: string,
    prescriptions: TPrescription[],
  ): Promise<{ success: true } | { success: false }> {
    const inactiveTreatmentIds: string[] = [];

    const treatmentStatusBlacklist = [
      'cancelled',
      'completed',
      'transferred',
      'inProgress.waitingForPrescription',
    ];

    for (const prescription of prescriptions) {
      if (!prescription.treatment) {
        throw new PgQueueJobFailedError(
          `Treatment for prescription ${prescription.id} does not exist`,
          {
            patientId: patientId,
            prescriptionId: prescription.id,
          },
        );
      }

      if (treatmentStatusBlacklist.includes(prescription.treatment.status)) {
        inactiveTreatmentIds.push(prescription.treatment.id);
      }
    }

    if (inactiveTreatmentIds.length > 0) {
      if (inactiveTreatmentIds.length < prescriptions.length) {
        throw new PgQueueJobFailedError(`some Treatments are not active`, {
          patientId: patientId,
          inactiveTreatmentIds: inactiveTreatmentIds,
        });
      }
      return { success: false };
    }

    return { success: true };
  }

  private async assertSanityCheck(
    tx: PrismaTransactionalClient,
    patientId: string,
    prescriptions: TPrescription[],
    checks: {
      maxOldPrescriptionDays?: number;
    },
  ) {
    // check 2: all prescription are open
    for (const prescription of prescriptions) {
      if (prescription.status !== 'open') {
        throw new PgQueueJobFailedError(
          `Prescription ${prescription.id} is not open`,
          {
            patientId: patientId,
            prescriptionId: prescription.id,
            status: prescription.status,
          },
        );
      }
    }

    // check 3: all prescription don't have a stripeInvoiceId
    for (const prescription of prescriptions) {
      if (prescription.stripeInvoiceId) {
        throw new PgQueueJobFailedError(
          `Prescription ${prescription.id} already has a stripeInvoiceId`,
          {
            patientId: patientId,
            prescriptionId: prescription.id,
            stripeInvoiceId: prescription.stripeInvoiceId,
          },
        );
      }
    }

    // check 4: prescription not too old
    const now = new Date();
    for (const prescription of prescriptions) {
      const ageInDays = differenceInDays(now, prescription.createdAt);
      if (Math.abs(ageInDays) > checks.maxOldPrescriptionDays) {
        throw new PgQueueJobFailedError(
          `Prescription ${prescription.id} is too old`,
          {
            patientId: patientId,
            prescriptionId: prescription.id,
            ageInDays,
            maxOldPrescriptionDays: checks.maxOldPrescriptionDays,
          },
        );
      }
    }
  }

  private async assertOnlyUniqueNewRefillsCheck(
    tx: PrismaTransactionalClient,
    patientId: string,
    prescriptions: TPrescription[],
  ) {
    // step 1: check payload doesn't have duplicate treatmentId-refill
    // assert all prescption have unique treatmentId and refill combination
    const uniqueRefills = new Set<string>();
    for (const prescription of prescriptions) {
      const key = `${prescription.treatmentId}-${prescription.refill}`;
      if (uniqueRefills.has(key)) {
        throw new PgQueueJobFailedError(
          `Duplicate refill found for treatmentId ${prescription.treatmentId} and refill ${prescription.refill}`,
          {
            patientId,
            treatmentId: prescription.treatmentId,
            refill: prescription.refill,
            prescription: prescriptions.filter(
              (p) =>
                p.treatmentId === prescription.treatmentId &&
                p.refill === prescription.refill,
            ),
          },
        );
      }
      uniqueRefills.add(key);
    }

    // step 2: no existing open prescription for a treatmentId-refill
    const openPrescriptions = await tx.prescription.findMany({
      where: {
        id: { notIn: prescriptions.map((p) => p.id) },
        treatmentId: { in: prescriptions.map((p) => p.treatmentId) },
        refill: { in: prescriptions.map((p) => p.refill) },
        status: { in: ['queued', 'open', 'paid'] },
      },
    });

    // step3: check is latest prescription for refill
    for (const prescription of prescriptions) {
      const latestPrescription = await tx.prescription.findFirst({
        where: {
          treatmentId: prescription.treatmentId,
          refill: prescription.refill,
        },
        orderBy: { createdAt: 'desc' },
      });

      if (latestPrescription && latestPrescription.id !== prescription.id) {
        throw new PgQueueJobFailedError(
          `Prescription ${prescription.id} is not the latest for refill ${prescription.refill}`,
          {
            patientId,
            prescriptionId: prescription.id,
            refill: prescription.refill,
            latestPrescriptionId: latestPrescription.id,
          },
        );
      }
    }

    if (openPrescriptions.length > 0) {
      throw new PgQueueJobFailedError(`Duplicate open prescription found`, {
        patientId,
      });
    }
  }

  private async assertInvoiceDoesntExistInStripeCheck(
    patientId: string,
    prescriptions: TPrescription[],
  ) {
    for (const prescription of prescriptions) {
      {
        // TODO: after 05 october 2025 delete this section
        const existingInvoice = await this.stripe.listInvoices({
          customerId: patientId,
          metadata: {
            // the r_ was removed from the key
            [`r_${prescription.treatmentId}_${prescription.refill}`]: {
              exists: true,
            },
          },
        });

        if (
          existingInvoice.data.some((d) =>
            ['paid', 'open', 'draft'].includes(d.status),
          )
        ) {
          throw new PgQueueJobFailedError(
            `Invoice already exists for prescription ${prescription.id} and refill ${prescription.refill}`,
            {
              patientId: patientId,
              prescriptionId: prescription.id,
              refill: prescription.refill,
              existingInvoices: existingInvoice.data.map((inv) => inv.id),
            },
          );
        }
      }

      const existingInvoice2 = await this.stripe.listInvoices({
        customerId: patientId,
        metadata: {
          [`${prescription.treatmentId}_${prescription.refill}`]: {
            exists: true,
          },
        },
        limit: 10,
      });

      if (
        existingInvoice2.data.some((d) =>
          ['paid', 'open', 'draft'].includes(d.status),
        )
      ) {
        throw new PgQueueJobFailedError(
          `Invoice already exists for prescription ${prescription.id} and refill ${prescription.refill}`,
          {
            patientId: patientId,
            prescriptionId: prescription.id,
            refill: prescription.refill,
            existingInvoices: existingInvoice2.data.map((inv) => inv.id),
          },
        );
      }
    }
  }

  private async createInvoice(
    patient: TPatient,
    prescriptions: TPrescription[],
  ) {
    let invoiceId: string;
    let updatedPrescriptions;
    try {
      const invoice = await this.stripe.createInvoice(
        patient.stripeCustomerId,
        {
          // description: `Invoice for patient ${patient.id}`,
          metadata: this.generateInvoiceMetadata(patient.id, prescriptions),
        },
      );

      if (!invoice) {
        throw new PgQueueJobFailedError(
          `Failed to create invoice for patient ${patient.id}`,
        );
      }

      invoiceId = invoice.id;

      const invoiceItemIds: string[] = [];
      for (const prescription of prescriptions) {
        const item = await this.stripe.addInvoiceItem({
          invoiceId: invoice.id,
          customerId: patient.stripeCustomerId,
          item: {
            type: 'product',
            quantity: prescription.treatment.vials,
            priceId: prescription.productPriceId,
            metadata: {
              prescriptionId: prescription.id,
              treatmentId: prescription.treatmentId,
              refill: prescription.refill,
            },
          },
          couponId: prescription.stripeCouponId ?? undefined,
        });
        invoiceItemIds.push(item.id);
      }

      const couponIds = [
        ...new Set(prescriptions.map((p) => p.stripeCouponId).filter(Boolean)),
      ];
      const invoiceCoupon = couponIds.length > 0 ? couponIds[0] : null;
      if (invoiceCoupon) {
        await this.stripe.applyCouponToInvoice(invoice.id, invoiceCoupon);
      }

      // attach invoiceId to prescriptions
      updatedPrescriptions = await this.prisma.$transaction(
        prescriptions.map((prescription, index) => {
          const stripeInvoiceItemId = invoiceItemIds[index];
          if (!stripeInvoiceItemId) {
            throw new Error(
              `Missing Stripe invoice item ID for prescription ${prescription.id}`,
            );
          }
          return this.prisma.prescription.update({
            where: { id: prescription.id },
            data: {
              stripeInvoiceId: invoice.id,
              status: 'open',
              stripeInvoiceItemId,
            },
            select: {
              id: true,
              treatmentId: true,
              stripeCouponId: true,
              stripeInvoiceId: true,
              stripeInvoiceItemId: true,
              status: true,
            },
          });
        }),
      );
    } catch (error) {
      if (invoiceId) {
        try {
          await this.stripe.deleteDraft(invoiceId);
        } catch (deleteError) {
          this.logger.error(
            deleteError,
            {
              stripeInvoiceId: invoiceId,
              patientId: patient.id,
            },
            `Failed to void invoice after creation = ${invoiceId}`,
          );
        }
      }
      throw error;
    }

    if (!invoiceId) {
      throw new PgQueueJobFailedError('Failed to create invoice', {
        patientId: patient.id,
      });
    }

    // TODO: add check to see if invoice is not a draft anymore
    // attemptInvoiceCollect doesn't throw: if invoice failed to finalize it will stay as a draft
    const invoice = await this.stripe.attemptInvoiceCollect(invoiceId);

    return {
      success: !!invoice,
      prescriptions: updatedPrescriptions,
      patientId: patient.id,
      stripeCustomerId: patient.stripeCustomerId,
      invoiceId,
      paid: invoice?.status === 'paid',
    };
  }

  private generateInvoiceMetadata(
    patientId: string,
    prescriptions: TPrescription[],
  ) {
    const prescriptionIdList = prescriptions.map((p) => p.id).sort();
    const treatmentIdList = [
      ...new Set(prescriptions.map((p) => p.treatmentId)),
    ].sort();

    const refillMetadata = prescriptions.reduce(
      (acc, prescription) => {
        acc[`${prescription.treatmentId}_${prescription.refill}`] = 'true';
        return acc;
      },
      {} as Record<string, unknown>,
    );

    return {
      patientId,
      prescriptionIdList,
      treatmentIdList,
      ...refillMetadata,
    };
  }

  private async voidPrescriptions(
    prescriptionIds: string[],
    prisma?: PrismaTransactionalClient,
  ) {
    await (prisma || this.prisma).prescription.updateMany({
      where: {
        id: { in: prescriptionIds },
      },
      data: {
        status: 'voided',
      },
    });
  }
}
