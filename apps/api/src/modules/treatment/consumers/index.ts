import { TreatmentUpdatedCancelPrescriptionConsumer } from './treatment-updated-cancel-prescription.consumer';
import { TreatmentUpdatedNotifyNextDoseConsumer } from './treatment-updated-notify-next-dose.consumer ';
import { TreatmentUpdatedNotifyNextRefillConsumer } from './treatment-updated-notify-next-refill.consumer';
import { TreatmentUpdatedNotifyTransferRefillConsumer } from './treatment-updated-notify-transfer-refill.consumer';
import { TreatmentUpdatedPrescribeToPharmacyConsumer } from './treatment-updated-prescribe-to-pharmacy.consumer';
import { TreatmentUpdatedPrescriptionCreateConsumer } from './treatment-updated-prescription-create.consumer';

export const TreatmentConsumers = [
  TreatmentUpdatedPrescriptionCreateConsumer,
  TreatmentUpdatedCancelPrescriptionConsumer,
  TreatmentUpdatedNotifyNextDoseConsumer,
  TreatmentUpdatedNotifyNextRefillConsumer,
  TreatmentUpdatedNotifyTransferRefillConsumer,
  TreatmentUpdatedPrescribeToPharmacyConsumer,
];
