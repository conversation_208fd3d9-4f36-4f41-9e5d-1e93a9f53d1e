import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import {
  PgTopicConsumer,
  PgTopicMessage,
} from '@/modules/shared/queue/pg-topic-consumer.abstract';
import {
  TreatmentUpdatedTopic,
  TreatmentUpdatedTopicEvent,
  TreatmentUpdatedTopicPayload,
} from '@/modules/treatment/topics/treatment-updated.topic';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TreatmentUpdatedCancelPrescriptionConsumer extends PgTopicConsumer<
  TreatmentUpdatedTopicPayload,
  TreatmentUpdatedTopicEvent
> {
  private logger: LoggerService;

  constructor(
    private readonly prisma: PrismaService,
    private readonly segment: SegmentService,
    loggerFactory: LoggerFactory,
    treatmentUpdatedTopic: TreatmentUpdatedTopic,
  ) {
    super(treatmentUpdatedTopic, {
      consumerName: 'treatment-updated-cancel-prescription.consumer',
      events: ['cancelled'],
      concurrency: 3,
    });
    this.logger = loggerFactory.createLogger(
      TreatmentUpdatedCancelPrescriptionConsumer.name,
    );
  }

  async onMessage({
    payload,
  }: PgTopicMessage<TreatmentUpdatedTopicPayload, TreatmentUpdatedTopicEvent>) {
    const treatmentId = payload.treatment.id;
    await this.prisma.$transaction(async (prisma) => {
      const treatment = await prisma.treatment.findFirstOrThrow({
        where: { id: treatmentId },
        include: {
          initialProductPrice: {
            select: { product: { select: { name: true } } },
          },
          prescription: true,
        },
      });

      const invoicesToVoid = new Set<string>();
      const prescriptionsToVoid = new Set<string>();
      const prescriptionsToDelete = new Set<string>();

      treatment.prescription.forEach((prescription) => {
        if (
          ['open', 'failed'].includes(prescription.status) &&
          prescription.stripeInvoiceId
        ) {
          invoicesToVoid.add(prescription.stripeInvoiceId);
        } else if (prescription.status === 'open') {
          prescriptionsToVoid.add(prescription.id);
        } else if (prescription.status === 'queued') {
          prescriptionsToDelete.add(prescription.id);
        }
      });

      // we never have draft invoices
      // for (const invoiceId of invoicesToVoid) {
      //   void this.stripeService.deleteDraft(invoiceId);
      // }

      if (prescriptionsToVoid.size > 0) {
        await prisma.prescription.updateMany({
          where: {
            id: { in: Array.from(prescriptionsToVoid) },
          },
          data: {
            status: 'voided',
            lastError: { message: 'Treatment cancelled' },
          },
        });
      }

      if (prescriptionsToDelete.size > 0) {
        await prisma.prescription.updateMany({
          where: {
            id: { in: Array.from(prescriptionsToDelete) },
          },
          data: {
            status: 'voided',
            lastError: { message: 'Treatment cancelled' },
          },
        });
      }

      await this.segment.track(
        treatment.patientId,
        'subscriptionCancelled',
        {
          properties: {
            productName: treatment.initialProductPrice.product.name,
          },
        },
        { prisma },
      );

      return {
        success: true,
        voidedPrescription: Array.from(prescriptionsToVoid),
        deletedPrescription: Array.from(prescriptionsToDelete),
      };
    });
  }
}
