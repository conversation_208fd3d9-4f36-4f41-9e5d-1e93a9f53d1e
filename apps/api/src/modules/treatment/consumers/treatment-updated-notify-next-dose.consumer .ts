import { runInDbTransaction } from '@/helpers/transaction';
import { SendMessageUseCase } from '@/modules/chat/use-cases/send-message.use-case';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { PgQueueJobFailedError } from '@/modules/shared/queue/pg-queue.abstract';
import {
  PgTopicConsumer,
  PgTopicMessage,
} from '@/modules/shared/queue/pg-topic-consumer.abstract';
import {
  TreatmentUpdatedTopic,
  TreatmentUpdatedTopicEvent,
  TreatmentUpdatedTopicPayload,
} from '@/modules/treatment/topics/treatment-updated.topic';
import { Injectable } from '@nestjs/common';
import { Product, ProductPrice } from '@prisma/client';

import { TreatmentNotificationService } from '../services/treatment-notification.service';
import { TreatmentTransferService } from '../services/treatment-transfer.service';
import {
  TreatmentMachineContext,
  TreatmentProduct,
} from '../states/treatment.state';

@Injectable()
export class TreatmentUpdatedNotifyNextDoseConsumer extends PgTopicConsumer<
  TreatmentUpdatedTopicPayload,
  TreatmentUpdatedTopicEvent
> {
  private logger: LoggerService;

  constructor(
    private readonly prisma: PrismaService,
    private readonly sendMessageUseCase: SendMessageUseCase,
    private readonly segment: SegmentService,
    private readonly treatmentNotificationService: TreatmentNotificationService,
    private readonly treatmentTransferService: TreatmentTransferService,
    loggerFactory: LoggerFactory,
    treatmentUpdatedTopic: TreatmentUpdatedTopic,
  ) {
    super(treatmentUpdatedTopic, {
      consumerName: 'treatment-updated-notify-next-dose.consumer',
      events: ['treatment_notify_nextDose'],
      concurrency: 3,
      policy: 'one-active-per-singleton-key',
    });
    this.logger = loggerFactory.createLogger(
      TreatmentUpdatedNotifyNextDoseConsumer.name,
    );
  }

  async onMessage({
    payload,
  }: PgTopicMessage<TreatmentUpdatedTopicPayload, TreatmentUpdatedTopicEvent>) {
    return runInDbTransaction(this.prisma, async (prisma) => {
      const treatment = await prisma.treatment.findFirst({
        where: { id: payload.treatment.id },
      });

      if (!treatment) {
        throw new PgQueueJobFailedError('Treatment not found', {
          treatmentId: payload.treatment.id,
        });
      }

      if (!['inProgress.waitingBetweenRefills'].includes(treatment.status)) {
        return {
          success: false,
          message: 'Treatment is not in a valid state to notify next dose',
          currentStatus: treatment.status,
        };
      }

      const { context } = payload.treatment.state as {
        context: TreatmentMachineContext;
      };
      const activeProduct = context.activeProduct;

      let nextProduct: Partial<TreatmentProduct>;
      let price: ProductPrice & { product: Product };

      // Check if this treatment has been transferred to another treatment
      if (payload.treatment.transferredTo) {
        // Find the final treatment in the transfer chain
        const finalTreatment =
          await this.treatmentTransferService.findFinalTransferredTreatment(
            payload.treatment.transferredTo,
          );

        if (!finalTreatment) {
          throw new PgQueueJobFailedError(
            'Could not find final transferred treatment',
            {
              treatmentId: payload.treatment.id,
              transferredTo: payload.treatment.transferredTo,
            },
          );
        }

        // Use the first product from the final transferred treatment
        price = finalTreatment.initialProductPrice;

        // Make sure we can access the dose
        if (!price || !price.dosageDescription)
          return {
            success: false,
            message: 'Could not find nextProduct info on final treatment',
            currentStatus: treatment.status,
          };

        nextProduct = { id: price.id, dose: price.dosageDescription };
      } else {
        // Use the next product from the current treatment
        nextProduct = context.products[context.currentRefill + 1];

        // don't send notification if next product is not found
        if (!nextProduct)
          return {
            success: false,
            message: 'Treatment is already on the last refill',
            currentStatus: treatment.status,
          };

        price = await this.prisma.productPrice.findFirst({
          where: { id: nextProduct.id },
          include: { product: true },
        });

        if (!price)
          return {
            success: false,
            message: 'Could not find price information for next product',
            currentStatus: treatment.status,
            id: nextProduct.id,
          };
      }
      const { conversationId, doctorUserId } =
        await this.treatmentNotificationService.getConversationAndPatientName(
          context.patientId,
          { prisma },
        );

      const message: string =
        this.treatmentNotificationService.generateRefillMessage(
          price,
          activeProduct,
          context.refillSystem,
        );

      await this.sendMessageUseCase.execute(
        {
          content: message,
          contentType: 'text',
          role: 'Doctor',
          conversationId,
          userId: doctorUserId,
          type: 'system',
        },
        { prisma },
      );

      await this.segment.track(
        context.patientId,
        'dosageNotification',
        {
          properties: {
            productName: activeProduct.name,
            currentDose: activeProduct.dose,
            newDose: nextProduct.dose,
          },
        },
        { prisma },
      );

      // Check if this is a transferred treatment and it's injectable
      if (
        payload.treatment.transferredTo &&
        price.product.form === 'injectable'
      ) {
        // Send the transfer notification message directly
        // This handles the case where transfer happened before dose notification
        await this.treatmentTransferService.sendTransferNotification(
          payload.treatment,
          context,
        );
      }
    });
  }
}
