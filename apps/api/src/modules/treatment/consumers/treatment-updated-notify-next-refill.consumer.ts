import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import { getProductForAnalytics } from '@/modules/shared/helpers/generic';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import {
  PgTopicConsumer,
  PgTopicMessage,
} from '@/modules/shared/queue/pg-topic-consumer.abstract';
import {
  TreatmentUpdatedTopic,
  TreatmentUpdatedTopicEvent,
  TreatmentUpdatedTopicPayload,
} from '@/modules/treatment/topics/treatment-updated.topic';
import { Injectable } from '@nestjs/common';

import { TreatmentMachineContext } from '../states/treatment.state';

@Injectable()
export class TreatmentUpdatedNotifyNextRefillConsumer extends PgTopicConsumer<
  TreatmentUpdatedTopicPayload,
  TreatmentUpdatedTopicEvent
> {
  private logger: LoggerService;

  constructor(
    private readonly prisma: PrismaService,
    private readonly segment: SegmentService,
    loggerFactory: LoggerFactory,
    treatmentUpdatedTopic: TreatmentUpdatedTopic,
  ) {
    super(treatmentUpdatedTopic, {
      consumerName: 'treatment-updated-notify-next-refill.consumer',
      events: ['treatment_notify_nextRefill'],
      concurrency: 3,
      policy: 'one-active-per-singleton-key',
    });
    this.logger = loggerFactory.createLogger(
      TreatmentUpdatedNotifyNextRefillConsumer.name,
    );
  }

  async onMessage({
    payload,
  }: PgTopicMessage<TreatmentUpdatedTopicPayload, TreatmentUpdatedTopicEvent>) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };

    // no other refills to notify about
    if (context.currentRefill >= context.refills)
      return {
        success: false,
        message: 'No more refills to notify about',
      };

    const activeProduct = context.activeProduct;
    const nextProduct = context.products[context.currentRefill + 1];
    let analyticsProduct: ReturnType<typeof getProductForAnalytics> | null =
      null;

    if (!nextProduct) return;

    //new treatments have dosageLabel in the context, not the same as dose wich is only the mg or ml, dosageLabel provides more info
    //by example: dose = 7.5 dosageLabel = 7.5ml - 50 units
    //for old treatments we need to fetch it from the DB
    if (nextProduct.dosageLabel) {
      analyticsProduct = getProductForAnalytics({
        dosageLabel: activeProduct.dosageLabel,
        form: activeProduct.form,
        label: activeProduct.name,
      });
    } else {
      const nextProductPrice = await this.prisma.productPrice.findFirst({
        where: {
          id: nextProduct.id,
        },
        include: { product: true },
      });
      analyticsProduct = getProductForAnalytics({
        dosageLabel: nextProductPrice?.dosageLabel,
        form: nextProductPrice?.product.form,
        label: nextProductPrice?.product.label,
      });
    }

    await this.segment.track(context.patientId, 'refillNotification', {
      properties: {
        refillDate: context.nextRefillDate,
        ...analyticsProduct,
      },
    });

    return {
      success: true,
      refillDate: context.nextRefillDate,
      ...analyticsProduct,
    };
  }
}
