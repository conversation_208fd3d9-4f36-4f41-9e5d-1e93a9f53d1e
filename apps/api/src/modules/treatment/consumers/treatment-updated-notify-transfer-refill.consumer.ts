import { SendMessageUseCase } from '@/modules/chat/use-cases/send-message.use-case';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import {
  PgTopicConsumer,
  PgTopicMessage,
} from '@/modules/shared/queue/pg-topic-consumer.abstract';
import {
  TreatmentUpdatedTopic,
  TreatmentUpdatedTopicEvent,
  TreatmentUpdatedTopicPayload,
} from '@/modules/treatment/topics/treatment-updated.topic';
import { Injectable } from '@nestjs/common';
import { Treatment } from '@prisma/client';

import { TreatmentNotificationService } from '../services/treatment-notification.service';
import { TreatmentTransferService } from '../services/treatment-transfer.service';
import { TreatmentMachineContext } from '../states/treatment.state';

@Injectable()
export class TreatmentUpdatedNotifyTransferRefillConsumer extends PgTopicConsumer<
  TreatmentUpdatedTopicPayload,
  TreatmentUpdatedTopicEvent
> {
  private logger: LoggerService;

  constructor(
    private readonly prisma: PrismaService,
    private readonly sendMessageUseCase: SendMessageUseCase,
    private readonly segment: SegmentService,
    private readonly treatmentNotificationService: TreatmentNotificationService,
    private readonly treatmentTransferService: TreatmentTransferService,

    loggerFactory: LoggerFactory,
    treatmentUpdatedTopic: TreatmentUpdatedTopic,
  ) {
    super(treatmentUpdatedTopic, {
      consumerName: 'treatment-updated-notify-transfer-refill.consumer',
      events: ['treatment_notify_transferRefill'],
      concurrency: 3,
      policy: 'one-active-per-singleton-key',
    });
    this.logger = loggerFactory.createLogger(
      TreatmentUpdatedNotifyTransferRefillConsumer.name,
    );
  }

  async onMessage({
    payload,
  }: PgTopicMessage<TreatmentUpdatedTopicPayload, TreatmentUpdatedTopicEvent>) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };

    // This should only be called for treatments that were transferred
    if (!payload.treatment.transferredTo) {
      this.logger.error('Transfer refill event called without transferredTo', {
        payload,
      });
      return;
    }

    // Find the final treatment in the transfer chain
    const finalTreatment =
      await this.treatmentTransferService.findFinalTransferredTreatment(
        payload.treatment.transferredTo,
      );

    if (!finalTreatment) {
      this.logger.error(
        `Could not find final transferred treatment starting from ${payload.treatment.transferredTo}`,
        { payload },
      );
      return;
    }

    // Get the product to check if it's injectable
    const price = finalTreatment.initialProductPrice;

    if (!price) {
      this.logger.error(
        'Product price not found for transfer refill notification',
        { payload },
      );
      return;
    }

    // Always handle the dose update for the transferred treatment
    await this.handleTransferDoseUpdate(payload.treatment, context);

    // Additionally, for injectable products, send the transfer notification
    if (price.product.form === 'injectable') {
      await this.treatmentTransferService.sendTransferNotification(
        payload.treatment,
        context,
      );
    }
  }

  private async handleTransferDoseUpdate(
    treatment: Treatment,
    context: TreatmentMachineContext,
  ) {
    const activeProduct = context.activeProduct;

    // Find the final treatment in the transfer chain
    const finalTreatment =
      await this.treatmentTransferService.findFinalTransferredTreatment(
        treatment.transferredTo,
      );

    if (!finalTreatment) {
      this.logger.error(
        `Could not find final transferred treatment starting from ${treatment.transferredTo}`,
        { treatment },
      );
      return;
    }

    // Use the first product from the final transferred treatment
    const price = finalTreatment.initialProductPrice;

    // Make sure we can access the dose
    if (!price || !price.dosageDescription) {
      this.logger.error(
        'No dosage description found for transferred treatment',
        { treatment },
      );
      return;
    }

    const nextProduct = { id: price.id, dose: price.dosageDescription };

    // Get conversation info
    const { conversationId, doctorUserId } =
      await this.treatmentNotificationService.getConversationAndPatientName(
        context.patientId,
      );

    // Calculate the new message (same format as in nextDose method)
    const newMessage = this.treatmentNotificationService.generateRefillMessage(
      price,
      activeProduct,
      context.refillSystem,
    );

    // Get the last system message from the conversation
    const lastSystemMessage = await this.prisma.conversationMessage.findFirst({
      where: { conversationId, type: 'system' },
      orderBy: { createdAt: 'desc' },
    });

    // If the last system message is the same as the new message, no need to send an update
    if (lastSystemMessage && lastSystemMessage.content === newMessage) {
      this.logger.debug(
        'New dose is the same as previous dose, no update needed',
        { treatment },
      );
      return;
    }

    // Generate update message using template system
    const updateMessage =
      this.treatmentNotificationService.generateUpdateMessage(
        price,
        context.refillSystem,
      );

    await this.sendMessageUseCase.execute({
      content: updateMessage,
      contentType: 'text',
      role: 'Doctor',
      conversationId,
      userId: doctorUserId,
      type: 'system',
    });

    await this.segment.track(context.patientId, 'dosageNotification', {
      properties: {
        productName: activeProduct.name,
        currentDose: activeProduct.dose,
        newDose: nextProduct.dose,
        isUpdate: true,
      },
    });
  }
}
