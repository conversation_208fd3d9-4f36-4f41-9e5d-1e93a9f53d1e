import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import {
  PgTopicConsumer,
  PgTopicMessage,
} from '@/modules/shared/queue/pg-topic-consumer.abstract';
import {
  TreatmentUpdatedTopic,
  TreatmentUpdatedTopicEvent,
  TreatmentUpdatedTopicPayload,
} from '@/modules/treatment/topics/treatment-updated.topic';
import { Injectable } from '@nestjs/common';
import { prescriptionStatus } from '@prisma/client';

import { TreatmentMachineContext } from '../states/treatment.state';

@Injectable()
export class TreatmentUpdatedPrescriptionCreateConsumer extends PgTopicConsumer<
  TreatmentUpdatedTopicPayload,
  TreatmentUpdatedTopicEvent
> {
  private logger: LoggerService;

  constructor(
    private readonly prisma: PrismaService,
    private readonly segment: SegmentService,

    loggerFactory: LoggerFactory,
    treatmentUpdatedTopic: TreatmentUpdatedTopic,
  ) {
    super(treatmentUpdatedTopic, {
      consumerName: 'treatment-updated-prescription-create.consumer',
      events: ['prescription_create'],
      concurrency: 5,
      policy: 'one-active-per-singleton-key',
    });
    this.logger = loggerFactory.createLogger(
      TreatmentUpdatedPrescriptionCreateConsumer.name,
    );
  }

  async onMessage({
    payload,
  }: PgTopicMessage<TreatmentUpdatedTopicPayload, TreatmentUpdatedTopicEvent>) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };

    const { patientId, treatmentId } = context;
    const product = context.activeProduct!;
    const invoiceId = (context as unknown as { invoiceId: string }).invoiceId;

    await this.prisma.$transaction(async (prisma) => {
      const patient = await prisma.patient.findUniqueOrThrow({
        where: { id: patientId },
        include: { doctor: true, user: true },
      });

      if (!patient.doctor) {
        throw new Error('Patient does not have an assigned doctor');
      }

      const productPrice = await prisma.productPrice.findUniqueOrThrow({
        where: { id: product.id },
        select: {
          metadata: true,
          product: { select: { id: true, metadata: true } },
        },
      });

      if (invoiceId) {
        const existingPrescriptions = await prisma.prescription.findMany({
          where: {
            treatmentId,
            stripeInvoiceId: invoiceId,
            status: prescriptionStatus.failed,
          },
        });

        if (existingPrescriptions.length) {
          const ids = existingPrescriptions.map((p) => p.id);
          await prisma.prescription.updateMany({
            where: { id: { in: ids }, stripeInvoiceId: invoiceId },
            data: {
              status: prescriptionStatus.queued,
              updatedAt: new Date(),
              lastError: null,
            },
          });
          this.logger.debug(
            `Updated existing prescriptions ${ids.join(', ')} to queued status`,
          );
          return;
        }
      }

      // If not retrying or no existing failed prescription found, create a new one
      await prisma.prescription.create({
        data: {
          patient: { connect: { id: patientId } },
          doctor: { connect: { id: patient.doctor.id } },
          treatment: { connect: { id: treatmentId } },
          product: { connect: { id: productPrice.product.id } },
          productPrice: { connect: { id: product.id } },
          pharmacy: { connect: { id: patient.pharmacyId } },
          status: prescriptionStatus.queued,
          refill: context.currentRefill,
          stripeCouponId: product.coupon,
          stripeInvoiceId: null,
        },
      });
      this.logger.log('Created new prescription');

      // send current _core_ treatment identify to Segment
      if (productPrice.product.metadata['type'] === 'core') {
        await this.segment.identify(patient.user.id, {
          traits: { currentTreatment: productPrice.metadata['label'] },
        });
      }
    });
  }
}
