import { runInDbTransaction } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { LoggerFactory } from '@/modules/shared/logger/logger.service';
import {
  JobMetadata,
  PgQueue,
  PgQueueJobFailedError,
} from '@/modules/shared/queue/pg-queue.abstract';
import {
  TreatmentUpdatedTopic,
  TreatmentUpdatedTopicEvent,
} from '@/modules/treatment/topics/treatment-updated.topic';
import { Injectable, LoggerService } from '@nestjs/common';

import {
  TreatmentService,
  TreatmentSnapshot,
} from '../services/treatment.service';

export type TreatmentNextQueuePayload = {
  treatmentId: string;
};

@Injectable()
export class TreatmentNextQueue extends PgQueue<TreatmentNextQueuePayload> {
  private readonly logger: LoggerService;

  constructor(
    private readonly prisma: PrismaService,
    private readonly treatmentService: TreatmentService,
    private readonly treatmentUpdatedTopic: TreatmentUpdatedTopic,
    loggerFactory: LoggerFactory,
  ) {
    super('treatment-next.queue', {
      policy: 'one-active-per-singleton-key',
      concurrency: 5,
      retryDelay: 10,
      retryBackoff: true,
    });
    this.logger = loggerFactory.createLogger(TreatmentNextQueue.name);
  }

  work(payload: TreatmentNextQueuePayload, metadata: JobMetadata) {
    return runInDbTransaction(this.prisma, async (prisma) => {
      let treatment = await prisma.treatment.findUnique({
        where: { id: payload.treatmentId },
      });

      const initialStatus = treatment.status;
      const initialState = (treatment.state as { value: object })?.value;

      if (!treatment) {
        this.logger.warn('[UpdateTreatmentsJob] Treatment not found', {
          treatmentId: payload.treatmentId,
        });
        throw new Error('Treatment not found');
      }

      const treatmentEventsToEmit: {
        event: TreatmentUpdatedTopicEvent;
      }[] = [];
      const actor = await this.treatmentService.getActor(
        treatment.id,
        (e) => {
          treatmentEventsToEmit.push(e);
        },
        treatment.state as unknown as TreatmentSnapshot,
      );
      const snapshot = actor.getSnapshot();

      if (snapshot.can({ type: 'next' })) {
        actor.send({ type: 'next' });
        treatment = await this.treatmentService.updateTreatmentRecord(actor, {
          prisma,
        });
      } else {
        throw new PgQueueJobFailedError("can't send next", {
          treatmentId: treatment.id,
          treatmentState: initialState,
          treatmentStatus: initialStatus,
        });
      }

      for (const { event } of treatmentEventsToEmit) {
        await this.treatmentUpdatedTopic.publish(event, treatment, {
          prisma,
          source: 'treatment-next.queue',
        });
      }

      const newState = (treatment.state as { value: object })?.value;
      if (
        newState === initialState &&
        treatment.status === initialStatus &&
        treatment.nextEventIn === null
      ) {
        throw new PgQueueJobFailedError('next event did not change treatment', {
          treatmentId: treatment.id,
          initialStatus,
          initialState,
          newStatus: treatment.status,
          newState: (treatment.state as { value: object })?.value,
          emittedEvents: treatmentEventsToEmit,
          nextEventIn: treatment.nextEventIn,
        });
      }

      return {
        treatmentId: treatment.id,
        initialStatus,
        initialState,
        newStatus: treatment.status,
        newState: (treatment.state as { value: object })?.value,
        emittedEvents: treatmentEventsToEmit,
        nextEventIn: treatment.nextEventIn,
      };
    });
  }
}
