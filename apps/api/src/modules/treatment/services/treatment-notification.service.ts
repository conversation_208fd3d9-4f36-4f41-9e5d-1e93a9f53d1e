import { ExecutionContext } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { replaceTemplate } from '@/modules/shared/helpers/generic';
import { Injectable, Logger } from '@nestjs/common';
import { Product, ProductForms, ProductPrice } from '@prisma/client';

import {
  ActiveTreatmentProduct,
  RefillSystem,
} from '../states/treatment.state';

@Injectable()
export class TreatmentNotificationService {
  private readonly logger = new Logger(TreatmentNotificationService.name);

  private readonly updateMessageTemplates: Record<ProductForms, string> = {
    injectable:
      'Update regarding your next refill: Please use {dose} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    oral: 'Update regarding your next refill: Please use {dose} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    tablet:
      'Update regarding your next refill: Please use {dose} {dosageTimeframe}. {dosageAdditionalMessage}',
  };

  private readonly messageTemplates: Record<ProductForms, string> = {
    injectable:
      'Hi, your next refill of {refill} will be processing in 2 days. Please use {dose} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    oral: 'Hi, your next refill of {refill} will be processing in 2 days. Please use {dose} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    tablet:
      'Hi, your next refill of {refill} will be processing in 2 days. Please use {dose} {dosageTimeframe}. {dosageAdditionalMessage}',
  };

  constructor(private readonly prisma: PrismaService) {}

  async getConversationAndPatientName(
    patientId: string,
    ctx: ExecutionContext = { prisma: this.prisma },
  ) {
    const result = await ctx.prisma.patient.findFirst({
      where: { id: patientId },
      select: {
        conversations: {
          where: { type: 'patientDoctor' },
          select: { id: true, type: true },
        },
        doctor: { select: { userId: true } },
      },
    });

    const patientDoctorConversation = result.conversations?.find(
      (c) => c.type === 'patientDoctor',
    );
    return {
      conversationId: patientDoctorConversation.id,
      doctorUserId: result.doctor.userId,
    };
  }

  generateUpdateMessage(
    price: ProductPrice & { product: Product },
    refillSystem: RefillSystem,
  ) {
    // Get the form from metadata, defaulting to 'injectable' if not found
    const form = price.product.form;

    // Get the update template for this form type
    const updateTemplate =
      this.updateMessageTemplates[form] ||
      this.updateMessageTemplates.injectable;

    const dose = `${price.dosageDescription} / ${price.milligrams}mg`;

    const templateData = {
      dose,
      dosageTimeframe: price.dosageTimeframe,
      dosageAdditionalMessage:
        refillSystem === 'scaling' && price.dosageAdditionalMessage
          ? price.dosageAdditionalMessage
          : '',
    };

    return replaceTemplate(updateTemplate, templateData).trim();
  }

  generateRefillMessage(
    price: ProductPrice & { product: Product },
    activeTreatmentProduct: ActiveTreatmentProduct,
    refillSystem: RefillSystem,
  ) {
    const form = price.product.form;

    // Get the template for this form type
    const messageTemplate =
      this.messageTemplates[form] || this.messageTemplates.injectable;

    const dose = `${price.dosageDescription} / ${price.milligrams}mg`;

    const templateData = {
      refill: activeTreatmentProduct.name,
      dose,
      dosageTimeframe: price.dosageTimeframe,
      dosageAdditionalMessage:
        refillSystem === 'scaling' && price.dosageAdditionalMessage
          ? price.dosageAdditionalMessage
          : '',
    };

    return replaceTemplate(messageTemplate, templateData);
  }
}
