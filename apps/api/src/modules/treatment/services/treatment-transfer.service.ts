import { ExecutionContext } from '@/helpers/transaction';
import { SendMessageUseCase } from '@/modules/chat/use-cases/send-message.use-case';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { Pharmacy, Product, ProductPrice, Treatment } from '@prisma/client';

import { TreatmentMachineContext } from '../states/treatment.state';
import { TreatmentNotificationService } from './treatment-notification.service';

/**
 * Service to handle distributed locking for treatments
 * Provides a simplified interface for locking and state tracking
 */
@Injectable()
export class TreatmentTransferService {
  private readonly logger = new Logger(TreatmentTransferService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly sendMessageUseCase: SendMessageUseCase,
    private readonly treatmentNotificationService: TreatmentNotificationService,
  ) {}

  async sendTransferNotification(
    treatment: Treatment,
    context: TreatmentMachineContext,
    { prisma }: ExecutionContext = { prisma: this.prisma },
  ) {
    if (!treatment.transferredTo) {
      return;
    }

    // Find the final treatment in the transfer chain
    const finalTreatment = await this.findFinalTransferredTreatment(
      treatment.transferredTo,
      10,
      { prisma },
    );

    if (!finalTreatment) {
      this.logger.error(
        `Could not find final transferred treatment starting from ${treatment.transferredTo}`,
        { treatment },
      );
      return;
    }

    // Find the last treatment that actually delivered medication (was active)
    const lastActiveTreatment = await this.findLastActiveTreatmentInChain(
      treatment.id,
      { prisma },
    );

    if (!lastActiveTreatment) {
      this.logger.error(
        `No active treatment found in transfer chain for treatment ${treatment.id}`,
        { treatment, finalTreatment },
      );
      return;
    }

    const oldPharmacyName =
      lastActiveTreatment.initialProductPrice.product.pharmacy.name;
    const newPharmacyName =
      finalTreatment.initialProductPrice.product.pharmacy.name;
    const productName = finalTreatment.initialProductPrice.product.label;

    // Get additive benefit from the new pharmacy's product
    const additiveBenefit = (finalTreatment.initialProductPrice as any)
      .additiveBenefit as string | null;

    // Build dose mapping for all doses of the product
    const doseMapping = await this.buildDoseMapping(
      lastActiveTreatment.initialProductPrice.product.id,
      finalTreatment.initialProductPrice.product.id,
      oldPharmacyName,
      newPharmacyName,
    );

    // Generate the transfer message
    const message = this.generateTransferMessage(
      newPharmacyName,
      productName,
      additiveBenefit,
      doseMapping,
    );

    // Get conversation info
    const { conversationId, doctorUserId } =
      await this.treatmentNotificationService.getConversationAndPatientName(
        context.patientId,
      );

    // Send the message
    await this.sendMessageUseCase.execute({
      content: message,
      contentType: 'text',
      role: 'Doctor',
      conversationId,
      userId: doctorUserId,
      type: 'system',
    });
  }

  /**
   * Recursively follows the transferredTo chain to find the final treatment.
   * This handles cases where a treatment has been transferred multiple times.
   * @param treatmentId - The ID of the treatment to start from
   * @param maxDepth - Maximum depth to follow to prevent infinite loops
   * @returns The final treatment in the transfer chain
   */
  async findFinalTransferredTreatment(
    treatmentId: string,
    maxDepth: number = 10,
    { prisma }: ExecutionContext = { prisma: this.prisma },
  ): Promise<
    | (Treatment & {
        initialProductPrice: ProductPrice & {
          product: Product & { pharmacy: Pharmacy };
        };
      })
    | null
  > {
    let currentTreatment = await prisma.treatment.findUnique({
      where: { id: treatmentId },
      include: {
        initialProductPrice: {
          include: { product: { include: { pharmacy: true } } },
        },
      },
    });

    if (!currentTreatment) {
      return null;
    }

    let depth = 0;
    const visitedIds = new Set<string>([currentTreatment.id]);

    // Follow the transfer chain
    while (currentTreatment.transferredTo && depth < maxDepth) {
      // Check for circular references
      if (visitedIds.has(currentTreatment.transferredTo)) {
        this.logger.error(
          `Circular transfer reference detected: ${currentTreatment.id} -> ${currentTreatment.transferredTo}`,
          { currentTreatment, treatmentId, depth, maxDepth, visitedIds },
        );
        break;
      }

      const nextTreatment = await prisma.treatment.findUnique({
        where: { id: currentTreatment.transferredTo },
        include: {
          initialProductPrice: {
            include: { product: { include: { pharmacy: true } } },
          },
        },
      });

      if (!nextTreatment) {
        this.logger.error(
          `Transferred treatment ${currentTreatment.transferredTo} not found`,
          { currentTreatment, treatmentId, depth, maxDepth, visitedIds },
        );
        break;
      }

      visitedIds.add(nextTreatment.id);
      currentTreatment = nextTreatment;
      depth++;
    }

    if (depth >= maxDepth) {
      this.logger.error(
        `Maximum transfer depth (${maxDepth}) reached for treatment ${treatmentId}`,
        { currentTreatment, treatmentId, depth, maxDepth, visitedIds },
      );
    }

    return currentTreatment;
  }

  /**
   * Finds the last treatment in the transfer chain that was actually active (delivered medication).
   * This is used to show transfers from the last pharmacy that delivered medication,
   * not from intermediate scheduled treatments that were cancelled.
   * @param treatmentId - The ID of the treatment to start from
   * @returns The last active treatment in the chain, or null if none found
   */
  async findLastActiveTreatmentInChain(
    treatmentId: string,
    { prisma }: ExecutionContext = { prisma: this.prisma },
  ): Promise<
    | (Treatment & {
        initialProductPrice: ProductPrice & {
          product: Product & { pharmacy: Pharmacy };
        };
      })
    | null
  > {
    let currentTreatment = await prisma.treatment.findUnique({
      where: { id: treatmentId },
      include: {
        initialProductPrice: {
          include: { product: { include: { pharmacy: true } } },
        },
      },
    });

    if (!currentTreatment) {
      return null;
    }

    // If this treatment was active (actually delivered medication), return it
    if (
      currentTreatment.status.includes('inProgress') ||
      currentTreatment.status === 'completed'
    ) {
      return currentTreatment;
    }

    // Otherwise, trace back through the transfer chain to find the last active treatment
    const visited = new Set<string>([currentTreatment.id]);

    while (currentTreatment) {
      // Find treatments that transferred TO this treatment
      const previousTreatment = await prisma.treatment.findFirst({
        where: {
          transferredTo: currentTreatment.id,
          id: { notIn: Array.from(visited) }, // Prevent circular references
        },
        include: {
          initialProductPrice: {
            include: { product: { include: { pharmacy: true } } },
          },
        },
      });

      if (!previousTreatment) {
        // No more treatments in the chain
        break;
      }

      visited.add(previousTreatment.id);

      // Check if this previous treatment was active
      if (
        previousTreatment.status.includes('inProgress') ||
        previousTreatment.status === 'completed'
      ) {
        return previousTreatment;
      }

      currentTreatment = previousTreatment;
    }

    // No active treatment found in the chain
    return null;
  }

  private generateTransferMessage(
    newPharmacyName: string,
    productName: string,
    additiveBenefit: string | null,
    doseMapping: Array<{ oldDose: string; newDose: string }>,
  ): string {
    let message = `In order to assure continued supply of the highest quality compounded GLP-1 medications, Willow maintains relationships with multiple pharmacies. It is sometimes necessary to change pharmacies both to improve quality and customer service as well as to ensure no disruption in supply.

Your ${productName} prescription will now be filled by ${newPharmacyName} and personalized to your needs.`;

    if (additiveBenefit) message += ` ${additiveBenefit}.`;

    if (doseMapping.length > 0) {
      message +=
        ' With this new formula, the dosing schedule may be slightly different:\n\n';
      message += doseMapping
        .map((dm) => `${dm.oldDose} → ${dm.newDose}`)
        .join('\n');
    }

    return message;
  }

  private async buildDoseMapping(
    oldProductId: string,
    newProductId: string,
    oldPharmacyName: string,
    newPharmacyName: string,
    ctx: ExecutionContext = { prisma: this.prisma },
  ): Promise<Array<{ oldDose: string; newDose: string }>> {
    // Get all product prices for both products
    const [oldProductPrices, newProductPrices] = await Promise.all([
      ctx.prisma.productPrice.findMany({
        where: {
          productId: oldProductId,
          active: true,
        },
        orderBy: {
          phase: 'asc',
        },
      }),
      ctx.prisma.productPrice.findMany({
        where: {
          productId: newProductId,
          active: true,
        },
        orderBy: {
          phase: 'asc',
        },
      }),
    ]);

    // Build mapping based on equivalence groups
    const mapping: Array<{ oldDose: string; newDose: string }> = [];

    for (const oldPrice of oldProductPrices) {
      // Find the corresponding new price by equivalence group
      const newPrice = newProductPrices.find(
        (np) =>
          np.equivalenceGroupId &&
          np.equivalenceGroupId === oldPrice.equivalenceGroupId,
      );

      if (newPrice && oldPrice.milligrams && newPrice.milligrams) {
        mapping.push({
          oldDose: `${oldPharmacyName} ${oldPrice.milligrams}mg dose`,
          newDose: `${newPharmacyName} ${newPrice.milligrams}mg dose`,
        });
      }
    }

    return mapping;
  }
}
