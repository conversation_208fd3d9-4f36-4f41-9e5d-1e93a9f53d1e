import { PrismaService } from '@/modules/prisma/prisma.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { PgQueueSendOptions } from '@/modules/shared/queue/pg-queue.abstract';
import { PgTopic } from '@/modules/shared/queue/pg-topic.abstract';
import { Injectable } from '@nestjs/common';
import { Treatment } from '@prisma/client';

export const TreatmentUpdatedTopicEventList = [
  'created',
  'end_date_changed',
  'resumed',
  'cancelled',
  'paused',
  // 'deleted', // TODO: we don't fire this event (double check and delete it)
  'completed',
  // internal_events
  'prescription_create',
  'prescription_prescribe',
  'treatment_delete',
  'treatment_notify_nextDose',
  'treatment_notify_nextRefill',
  'treatment_notify_transferRefill',
] as const;
export type TreatmentUpdatedTopicPayload = {
  treatment: Treatment;
  source?: string;
};
export type TreatmentUpdatedTopicEvent =
  (typeof TreatmentUpdatedTopicEventList)[number];

@Injectable()
export class TreatmentUpdatedTopic extends PgTopic<
  TreatmentUpdatedTopicPayload,
  TreatmentUpdatedTopicEvent
> {
  private readonly logger: LoggerService;

  constructor(
    private readonly prisma: PrismaService,
    loggerFactory: LoggerFactory,
  ) {
    super('treatment-updated.topic');
    this.logger = loggerFactory.createLogger(TreatmentUpdatedTopic.name);
  }

  async publish(
    event: TreatmentUpdatedTopicEvent,
    _treatment: string | Treatment,
    { source, ...ctx }: PgQueueSendOptions & { source?: string | null } = {
      source: null,
    },
  ) {
    if (!TreatmentUpdatedTopicEventList.includes(event)) {
      this.logger.error(`event ${event} not supported for topic ${this.name}`);
    }

    const prisma = ctx.prisma ?? this.prisma;
    let treatment = _treatment as Treatment;
    if (typeof _treatment === 'string') {
      treatment = await prisma.treatment.findUniqueOrThrow({
        where: { id: _treatment },
      });
    }

    let throttle = {};

    if (
      [
        'prescription_create',
        'prescription_prescribe',
        'treatment_delete',
        'treatment_notify_nextDose',
        'treatment_notify_nextRefill',
        'treatment_notify_transferRefill',
      ].includes(event)
    ) {
      throttle = {
        singletonPolicy: 'throttle',
        singletonIntervalInSeconds: 60, // 1 minute
      } as const;
    }

    await this.send(
      event,
      treatment.patientId,
      { treatment, source },
      {
        ...throttle,
        ...ctx,
        singletonKey: ctx?.singletonKey ?? `ti_${treatment.id}`,
      },
    );
  }
}
