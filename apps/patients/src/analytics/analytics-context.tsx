'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import Script from 'next/script';
import { AnalyticsBrowser } from '@segment/analytics-next';
import mixpanel from 'mixpanel-browser';

import { env } from '~/env';
import { useTracking } from './use-tracking';

export const analytics = AnalyticsBrowser.load({
  writeKey: env.NEXT_PUBLIC_SEGMENT_WRITE_KEY,
});

export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  // load mixpannel
  useEffect(() => {
    if (env.NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN) {
      console.log('Initializing Mixpanel');
      mixpanel.init(env.NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN, {
        autocapture: false,
        record_sessions_percent: 2,
        api_host: `${env.NEXT_PUBLIC_PATIENTS_URL}/mp`,
      });
    }
  }, []);

  useTrackPages();

  return (
    <>
      {children}
      <Script src="https://cdn.optimizely.com/js/29698630374.js" />
      <UniversalScriptComponent />
      <TranscendScriptComponent />
    </>
  );
}

const UniversalScriptComponent = () => {
  useEffect(() => {
    const head = document.head;
    const script = document.createElement('script');

    script.type = 'text/javascript';
    script.src =
      'https://t.startwillow.com/v1/lst/universal-script?ph=d2a60b67d55cdb1fcec727a9ab9acd784e0e178a29374a8a51b8b1329e6217fd&tag=!clicked&ref_url=' +
      encodeURIComponent(document.URL);

    head.appendChild(script);

    // Cleanup to remove the script when the component is unmounted
    return () => {
      head.removeChild(script);
    };
  }, []);

  return null;
};

function TranscendScriptComponent() {
  const isProduction = env.NEXT_PUBLIC_ENVIRONMENT === 'production';

  if (isProduction)
    return (
      <Script
        src="https://transcend-cdn.com/cm/1b419e87-1f5f-4574-9d0c-ead37fb421b1/airgap.js"
        data-cfasync="false"
      />
    );

  return (
    <Script
      src="https://transcend-cdn.com/cm-test/1b419e87-1f5f-4574-9d0c-ead37fb421b1/airgap.js"
      data-cfasync="false"
    />
  );
}

const useTrackPages = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { page } = useTracking();

  useEffect(() => {
    void page();
  }, [page, pathname, searchParams]);

  return null;
};
