'use client';

import React, { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import HomeNavbar from '@/components/nav/HomeNavbar';
import OnboardingLayout from '@/components/onboarding/OnboardingLayout';
import { onboardingVersionAtom } from '@/store/store';
import { useAtomValue } from 'jotai';

import { useTracking } from '~/analytics/use-tracking';

const UnifiedOnboardingLayout = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const pathname = usePathname();
  const { track } = useTracking();
  const onboardingVersion = useAtomValue(onboardingVersionAtom);

  useEffect(() => {
    track('Onboarding Screen Viewed', {
      version: onboardingVersion,
      path: pathname,
    });
  }, [track, onboardingVersion, pathname]);

  // For pre-signup flow, use the simpler layout with step counter
  const isPreSignup = pathname.includes('/pre-signup');

  if (isPreSignup) {
    return (
      <main className="relative flex h-full min-h-screen flex-col">
        <HomeNavbar />
        <div className="app-container flex flex-1 flex-grow flex-col">
          <OnboardingLayout withStepCounter={true}>{children}</OnboardingLayout>
        </div>
      </main>
    );
  }

  // For post-signup flow, use the existing connected layout structure
  return (
    <main className="relative flex h-full min-h-screen flex-col bg-biege">
      <HomeNavbar />
      <div className="app-container flex flex-1 flex-grow flex-col">
        {children}
      </div>
    </main>
  );
};

export default UnifiedOnboardingLayout;
