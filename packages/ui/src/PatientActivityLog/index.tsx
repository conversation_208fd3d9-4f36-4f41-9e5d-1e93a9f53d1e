import React from 'react';
import { startCase } from 'lodash';

import type { AuditLogApi } from '@willow/utils/audit-log/index';
import type { ActorExtraDetails } from '@willow/utils/audit-log/types';
import { Loader } from '@willow/ui/loader';

import { cn } from '..';
import { ActivityBubble } from './activity-bubble';
import { CreatePatientNote } from './create-patient-note';
import { IntercomMessageBubble } from './intercom-message-bubble';
import { NoteBubble } from './note-bubble';

export function PatientActivityLog({
  data,
  isLoading,
  error,
  onCreateNote,
  isCreateNotePending,
  isHideEvents,
  handleHideEvents,
  className,
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
}: {
  data: AuditLogApi[];
  isLoading: boolean;
  error?: Error | null;
  isHideEvents: boolean;
  onCreateNote?: (
    variables: {
      note: string;
    },
    options?: {
      onSuccess?: () => void;
    },
  ) => Promise<any>; // eslint-disable-line @typescript-eslint/no-explicit-any
  isCreateNotePending?: boolean;
  handleHideEvents: () => void;
  className?: string;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  fetchNextPage?: () => void;
}) {
  const handleScroll = React.useCallback(
    (e: React.UIEvent<HTMLDivElement>, fetchNextPage: () => void) => {
      const element = e.currentTarget;

      if (
        element.scrollTop + element.clientHeight >= element.scrollHeight - 20 &&
        hasNextPage &&
        !isFetchingNextPage
      )
        fetchNextPage();
    },
    [hasNextPage, isFetchingNextPage],
  );

  if (isLoading) return <Loader className="h-16" />;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className="h-full">
      <div
        className={cn(
          'flex h-full max-h-[90vh] flex-col gap-4 overflow-scroll px-10 pb-10 pt-4',
          {
            'max-h-[80vh]': !!onCreateNote,
          },
          className,
        )}
        onScroll={(e) => {
          if (fetchNextPage) handleScroll(e, fetchNextPage);
        }}
      >
        <div className="flex flex-row items-center justify-end">
          <div
            className="cursor-pointer text-xs font-bold"
            onClick={handleHideEvents}
          >
            {isHideEvents ? 'Show Events' : 'Hide Events'}
          </div>
        </div>
        {data.map((log) => (
          <ActivityLogBubble key={log.id} log={log} />
        ))}
        {isFetchingNextPage && <Loader className="h-16" />}
      </div>
      {onCreateNote && (
        <CreatePatientNote
          onCreateNote={onCreateNote}
          isPending={isCreateNotePending}
        />
      )}
    </div>
  );
}

function ActivityLogBubble({ log }: { log: AuditLogApi }) {
  // Patient
  if (log.action === 'INTERCOM_CONVERSATION_MESSAGE_CREATED') {
    console.log('log');
    console.log(log);
    return (
      <IntercomMessageBubble
        title="Intercom Message"
        date={new Date(log.createdAt)}
        details={log.details}
      ></IntercomMessageBubble>
    );
  }
  if (log.action === 'PATIENT_ACCOUNT_CREATED') {
    return (
      <ActivityBubble
        title="Account Created"
        date={new Date(log.createdAt)}
        state="success"
        indicator="🎂"
      />
    );
  }

  if (log.action === 'PATIENT_VISIT_STARTED') {
    return (
      <ActivityBubble
        title="Visit Started"
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      />
    );
  }

  if (log.action === 'PATIENT_ID_PHOTO_UPDATED') {
    return (
      <ActivityBubble
        title={
          log.details.type === 'face-photo' ? 'Photo Uploaded' : 'ID Uploaded'
        }
        date={new Date(log.createdAt)}
        state="success"
        indicator="📸"
      >
        {log.actorType !== 'PATIENT' && (
          <RenderActor
            label="Updated By"
            actorType={log.actorType}
            actorId={log.actorId}
            actorDetails={log.actorExtraDetails}
          />
        )}
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_CANCELLED') {
    return (
      <ActivityBubble
        title="Cancelled"
        date={new Date(log.createdAt)}
        state="success"
        indicator="❌"
      >
        <LableValue label="Reason" value={log.details.reason} />
        <LableValue label="Notes" value={log.details.note} />
        {log.actorType !== 'PATIENT' && (
          <RenderActor
            label="Cancelled By"
            actorType={log.actorType}
            actorId={log.actorId}
            actorDetails={log.actorExtraDetails}
          />
        )}
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_UNCANCELLED') {
    return (
      <ActivityBubble
        title="Uncancelled"
        date={new Date(log.createdAt)}
        state="success"
        indicator="❌"
      >
        {log.actorType !== 'PATIENT' && (
          <RenderActor
            label="Restored By"
            actorType={log.actorType}
            actorId={log.actorId}
            actorDetails={log.actorExtraDetails}
          />
        )}
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_REFUND_ON_CANCEL') {
    return (
      <ActivityBubble
        title="Automatic Refund on Cancellation"
        date={new Date(log.createdAt)}
        state="success"
        indicator="💰"
      >
        <LableValue
          label="Refunded Invoices"
          value={log.details.refundedInvoices.toString()}
        />
        <LableValue
          label="Total Amount"
          value={`$${(log.details.totalRefundedAmount / 100).toFixed(2)}`}
        />
        <RenderActor
          label="Processed By"
          actorType={log.actorType}
          actorId={log.actorId}
          actorDetails={log.actorExtraDetails}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_REFUND_ISSUED') {
    return (
      <ActivityBubble
        title="Refund Issued"
        date={new Date(log.createdAt)}
        state="success"
        indicator="💰"
      >
        <LableValue
          label="Amount"
          value={`$${log.details.refundedValue.toFixed(2)}`}
        />
        <LableValue
          label="Card"
          value={[
            log.details.cardName,
            log.details.cardLast4 && `**** **** **** ${log.details.cardLast4}`,
          ]
            .filter(Boolean)
            .join(' ')}
        />
        <RenderActor
          label="Processed By"
          actorType={log.actorType}
          actorId={log.actorId}
          actorDetails={log.actorExtraDetails}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_IDENTITY_ACCEPTED') {
    return (
      <ActivityBubble
        title="Identity Accepted"
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      >
        <RenderActor
          actorType={log.actorType}
          actorId={log.actorId}
          actorDetails={log.actorExtraDetails}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_ACCEPTED') {
    return (
      <ActivityBubble
        title="Patient Accepted"
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      >
        <LableValue label="Assiged to Doctor" value={log.details.doctorName} />
        <RenderActor
          actorType={log.actorType}
          actorId={log.actorId}
          actorDetails={log.actorExtraDetails}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_REJECTED') {
    return (
      <ActivityBubble
        title="Rejected"
        date={new Date(log.createdAt)}
        state="error"
        indicator="🚫"
      >
        <LableValue label="Notes" value={log.details.reason} />
        <LableValue label="Reason" value={log.details.status} />
        <RenderActor
          actorType={log.actorType}
          actorId={log.actorId}
          actorDetails={log.actorExtraDetails}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_PROFILE_INFO_UPDATED') {
    return (
      <ActivityBubble
        title="Profile Updated"
        date={new Date(log.createdAt)}
        state="error"
        indicator="✏️"
      >
        <RenderActor
          label="Updated By"
          actorType={log.actorType}
          actorId={log.actorId}
          actorDetails={log.actorExtraDetails}
        />
        <LableValue
          label="Old Values"
          value={JSON.stringify(log.details.old)}
        />
        <LableValue
          label="New Values"
          value={JSON.stringify(log.details.changes)}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_INFO_UPDATED') {
    return (
      <ActivityBubble
        title="Profile Updated"
        date={new Date(log.createdAt)}
        state="error"
        indicator="✏️"
      >
        {log.actorType !== 'PATIENT' && (
          <RenderActor
            label="Updated By"
            actorType={log.actorType}
            actorId={log.actorId}
            actorDetails={log.actorExtraDetails}
          />
        )}
        <LableValue
          label="New Values"
          value={JSON.stringify(log.details.changes)}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_SHIPPING_ADDRESS_UPDATED') {
    return (
      <ActivityBubble
        title="Shipping Updated"
        date={new Date(log.createdAt)}
        state="error"
        indicator="✏️"
      >
        <LableValue
          label="Updated By"
          value={`${log.actorType}: ${log.actorId}`}
        />
        <LableValue
          label="Old Values"
          value={JSON.stringify(log.details.old)}
        />
        <LableValue
          label="New Values"
          value={JSON.stringify(log.details.changes)}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_BILLING_ADDRESS_UPDATED') {
    return (
      <ActivityBubble
        title="Billing Address Updated"
        date={new Date(log.createdAt)}
        state="error"
        indicator="✏️"
      >
        <LableValue
          label="Updated By"
          value={`${log.actorType}: ${log.actorId}`}
        />
        <LableValue
          label="Old Address"
          value={JSON.stringify(log.details.old)}
        />
        <LableValue
          label="New Address"
          value={JSON.stringify(log.details.changes)}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_PAYMENT_INFO_UPDATED') {
    return (
      <ActivityBubble
        title="Payment Updated"
        date={new Date(log.createdAt)}
        state="error"
        indicator="✏️"
      >
        <LableValue
          label="Updated By"
          value={`${log.actorType}: ${log.actorId}`}
        />
        <LableValue label="Old Card" value={JSON.stringify(log.details.old)} />
        <LableValue
          label="New Card"
          value={log.details.new ? JSON.stringify(log.details.new) : 'N/A'}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_DELETED') {
    return (
      <ActivityBubble
        title="Deleted"
        date={new Date(log.createdAt)}
        state="error"
        indicator="🗑️"
      >
        <LableValue label="Deleted By" value={'Probably Ramiro'} />
      </ActivityBubble>
    );
  }

  // Onboarding
  if (log.action === 'ONBOARDING_TREATMENT_SELECTED') {
    return (
      <ActivityBubble
        title="Treatment Chosen"
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      >
        <LableValue label="Treatments">
          <div className="flex flex-col gap-1">
            {log.details.selectedTreatment.map((t) => (
              <span key={t.id} className="">
                - {t.name} ({t.id})
              </span>
            ))}
          </div>
        </LableValue>
      </ActivityBubble>
    );
  }

  if (log.action === 'ONBOARDING_TREATMENT_FORM_SELECTED') {
    return (
      <ActivityBubble
        title="Treatment Form Chosen"
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      >
        <LableValue
          label="Treatment Form"
          value={startCase(log.details.selectedForm)}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'ONBOARDING_SHIPPING_ADDRESS_UPDATED') {
    return (
      <ActivityBubble
        title="Shipping Updated"
        date={new Date(log.createdAt)}
        state="success"
        indicator="✏️"
      />
    );
  }

  if (log.action === 'ONBOARDING_CHECKOUT_STARTED') {
    return (
      <ActivityBubble
        title="Checkout Started"
        date={new Date(log.createdAt)}
        state="success"
        indicator="🛒"
      />
    );
  }

  if (log.action === 'ONBOARDING_CHECKOUT_COMPLETED') {
    return (
      <ActivityBubble
        title="Checkout Completed"
        date={new Date(log.createdAt)}
        state="success"
        indicator="💵"
      />
    );
  }

  if (log.action === 'TREATMENT_INVOICE_PAYMENT_FAILED') {
    return (
      <ActivityBubble
        title="Payment Failed"
        date={new Date(log.createdAt)}
        state="error"
        indicator="🚫"
      >
        <div>
          {`${log.details.product?.name} - $${log.details.product?.price}`}
        </div>

        {log.details.message && (
          <LableValue
            label="Failure Reason"
            value={startCase(log.details.message)}
          />
        )}
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_INVOICE_PAYMENT_FAILED') {
    return (
      <ActivityBubble
        title="Payment Failed"
        date={new Date(log.createdAt)}
        state="error"
        indicator="🚫"
      >
        <LableValue label="Items" />
        <ul className="list-disc px-4">
          {log.details.items.map((item) => (
            <li key={item.priceId}>
              {item.name} ({item.price})
            </li>
          ))}
        </ul>

        {log.details.message && (
          <LableValue
            label="Failure Reason"
            value={startCase(log.details.message)}
          />
        )}
      </ActivityBubble>
    );
  }

  if (log.action === 'ONBOARDING_QUESTIONNAIRE_COMPLETED') {
    return (
      <ActivityBubble
        title="Questionnaire Completed"
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      />
    );
  }

  if (log.action === 'ONBOARDING_UPLOAD_ID_PHOTO_SKIPPED') {
    return (
      <ActivityBubble
        title={
          log.details.type === 'face-photo'
            ? 'Photo Upload Skipped'
            : 'ID Upload Skipped'
        }
        date={new Date(log.createdAt)}
        state="error"
        indicator="➡️"
      />
    );
  }

  if (log.action === 'TREATMENT_CREATED') {
    return (
      <ActivityBubble
        title={'Prescription Written'}
        date={new Date(log.createdAt)}
        state="success"
        indicator="💊"
      >
        <LableValue
          label="Pharmacy"
          value={startCase(log.details.pharmacyName)}
        />
        <LableValue label="Product" value={log.details.productName} />
      </ActivityBubble>
    );
  }

  // Treatement
  if (log.action === 'PATIENT_REASSIGNED_DOCTOR') {
    return (
      <ActivityBubble
        title={'Patient Reassigned'}
        date={new Date(log.createdAt)}
        state="success"
        indicator="🔄"
      >
        <LableValue
          label="Old Doctor"
          value={`${startCase(log.details.old.firstName)} ${startCase(log.details.old.lastName)}`}
        />
        <LableValue
          label="New Doctor"
          value={`${startCase(log.details.new.firstName)} ${startCase(log.details.new.lastName)}`}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'TREATMENT_INVOICE_PAID') {
    return (
      <ActivityBubble
        title={`Treatment Invoice Paid`}
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      >
        {log.details.products && (
          <>
            <LableValue label="Products" />
            <ul className="list-disc px-4">
              {log.details.products.map((product) => (
                <li key={product.id}>{product.name}</li>
              ))}
            </ul>
          </>
        )}
        {log.details.total && (
          <LableValue
            label="Total Price"
            value={`$${(log.details.total / 100).toFixed(2)}`}
          />
        )}
        {log.details.amountPaid && (
          <LableValue
            label="Total Paid"
            value={`$${(log.details.amountPaid / 100).toFixed(2)}`}
          />
        )}
        <LableValue
          label="Card"
          value={[
            log.details.cardType,
            log.details.cardLast4 && `**** **** **** ${log.details.cardLast4}`,
          ]
            .filter(Boolean)
            .join(' ')}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'PATIENT_INVOICE_PAID') {
    return (
      <ActivityBubble
        title={`Treatment Invoice Paid`}
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      >
        <LableValue label="Products" />
        <ul className="list-disc px-4">
          {log.details.items.map((item) => (
            <li key={item.priceId}>
              {item.name} ({item.price})
            </li>
          ))}
        </ul>
        {log.details.total && (
          <LableValue
            label="Total Price"
            value={`$${(log.details.total / 100).toFixed(2)}`}
          />
        )}
        {log.details.amountPaid && (
          <LableValue
            label="Total Paid"
            value={`$${(log.details.amountPaid / 100).toFixed(2)}`}
          />
        )}
        <LableValue
          label="Card"
          value={[
            log.details.cardType,
            log.details.cardLast4 && `**** **** **** ${log.details.cardLast4}`,
          ]
            .filter(Boolean)
            .join(' ')}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'TREATMENT_ORDER_SENT') {
    return (
      <ActivityBubble
        title={`Treatment Order Sent`}
        date={new Date(log.createdAt)}
        state="success"
        indicator="📦"
      >
        <LableValue label="Treatment Id" value={log.resourceId} />
        <LableValue
          label="EIP Order Id"
          value={log.details.eipOrderId?.toString() ?? 'N/A'}
        />
        <LableValue label="Product" value={log.details.product.name} />
        <LableValue
          label="Quantity"
          value={log.details.product.quantity.toString()}
        />
        <LableValue label="Pharmacy" value={log.details.pharmacy} />
        <LableValue label="Form" value={log.details.form} />
      </ActivityBubble>
    );
  }

  if (log.action === 'TREATMENT_ORDER_FAILED') {
    return (
      <ActivityBubble
        title={`Treatment Order Failed`}
        date={new Date(log.createdAt)}
        state="error"
        indicator="❌"
      >
        <LableValue label="Treatment Id" value={log.resourceId} />
        <LableValue label="Order Id" value={log.details.orderId} />
        <LableValue label="Product" value={log.details.product.name} />
        <LableValue label="Error" value={log.details.error} />
      </ActivityBubble>
    );
  }

  if (log.action === 'TREATMENT_ORDER_SENT_MANUALLY') {
    return (
      <ActivityBubble
        title={`Order Sent Manually`}
        date={new Date(log.createdAt)}
        state="success"
        indicator="📝"
      >
        <LableValue label="Treatment Id" value={log.resourceId} />
        <LableValue label="Product" value={log.details.product.name} />
        <LableValue
          label="Quantity"
          value={log.details.product.quantity.toString()}
        />
        <LableValue label="Pharmacy" value={log.details.pharmacy} />
        <LableValue label="Form" value={log.details.form} />

        <RenderActor
          label="Prescribed By"
          actorType={log.actorType}
          actorId={log.actorId}
          actorDetails={log.actorExtraDetails}
        />
      </ActivityBubble>
    );
  }

  // Conversation
  if (log.action === 'CONVERSATION_CHAT_MESSAGE_CREATED') {
    return (
      <ActivityBubble
        title={`New Chat Message From ${startCase(log.actorType.toLowerCase())}`}
        date={new Date(log.createdAt)}
        state="success"
        indicator="💬"
      />
    );
  }

  if (log.action === 'FOLLOW_UP_CREATED') {
    return (
      <ActivityBubble
        title={`Follow Up Scheduled`}
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      >
        <LableValue label="Date" value={log.details.scheduledAt} />
        <LableValue label="Treatment Id" value={log.details.treatmentId} />
      </ActivityBubble>
    );
  }

  if (log.action === 'FOLLOW_UP_RESCHEDULED') {
    return (
      <ActivityBubble
        title="Follow-up Rescheduled"
        date={new Date(log.createdAt)}
        state="success"
        indicator="📅"
      >
        <LableValue
          label="Rescheduled to"
          value={new Date(log.details.scheduledAt).toLocaleDateString()}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'FOLLOW_UP_STARTED') {
    return (
      <ActivityBubble
        title={`Follow Up Started`}
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      />
    );
  }

  if (log.action === 'FOLLOW_UP_COMPLETED') {
    return (
      <ActivityBubble
        title="Follow Up Completed by Patient"
        date={new Date(log.createdAt)}
        state="success"
        indicator="✅"
      />
    );
  }

  if (log.action === 'FOLLOW_UP_REVIEWED_BY_DOCTOR') {
    return (
      <ActivityBubble
        title="Follow Up Reviewed"
        date={new Date(log.createdAt)}
        state="success"
        indicator="👨‍⚕️"
      >
        <RenderActor
          actorType={log.actorType}
          actorId={log.actorId}
          actorDetails={log.actorExtraDetails}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'FOLLOW_UP_CANCELLED') {
    return (
      <ActivityBubble
        title="Follow Up Cancelled"
        date={new Date(log.createdAt)}
        state="error"
        indicator="❌"
      >
        <LableValue label="Treatment Id" value={log.details.treatmentId} />
        <RenderActor
          actorType={log.actorType}
          actorId={log.actorId}
          actorDetails={log.actorExtraDetails}
        />
      </ActivityBubble>
    );
  }

  if (log.action === 'USER_PASSWORD_RESET_REQUESTED') {
    return (
      <ActivityBubble
        title={`Password Reset Requested`}
        date={new Date(log.createdAt)}
        state="success"
        indicator="🔒"
      ></ActivityBubble>
    );
  }
  if (log.action === 'USER_PASSWORD_RESET') {
    return (
      <ActivityBubble
        title={`Password Reset`}
        date={new Date(log.createdAt)}
        state="success"
        indicator="🔒"
      ></ActivityBubble>
    );
  }
  if (log.action === 'PATIENT_NOTE_CREATED') {
    return (
      <NoteBubble
        note={log.details.note}
        date={new Date(log.createdAt)}
        actorType={log.actorType}
        actorDetails={log.actorExtraDetails}
      />
    );
  }

  if (log.action === 'PRESCRIPTION_TRANSFERRED') {
    const details = log.details as {
      state: string;
      doctorName: string;
      oldPharmacy: string;
      newPharmacy: string;
      productType: string;
      productForm: string;
      status?: string;
    };
    return (
      <ActivityBubble
        title="Prescription Transferred"
        date={new Date(log.createdAt)}
        state="success"
        indicator="🔄"
      >
        <LableValue label="State" value={details.state} />
        <LableValue label="Doctor" value={details.doctorName} />
        <LableValue label="From Pharmacy" value={details.oldPharmacy} />
        <LableValue label="To Pharmacy" value={details.newPharmacy} />
        <LableValue label="Product Type" value={details.productType} />
        <LableValue label="Product Form" value={details.productForm} />
        {details.status && <LableValue label="Status" value={details.status} />}
      </ActivityBubble>
    );
  }
}

function LableValue({
  label,
  value,
  children,
}: {
  label: string;
  value?: string;
  children?: React.ReactNode;
}) {
  return (
    <div className="flex flex-col">
      <span className="text-bw-50 text-xs">{label}</span>
      {value && <span>{value}</span>}
      {children}
    </div>
  );
}

const RenderActor = ({
  label,
  actorType,
  actorId,
  actorDetails,
}: {
  label?: string;
  actorType: AuditLogApi['actorType'];
  actorId: string;
  actorDetails?: ActorExtraDetails;
}) => {
  return (
    <LableValue label={label ?? 'By'}>
      <div className="flex flex-col gap-1">
        <span>
          {startCase(actorType.toLowerCase())}: {actorId}
        </span>
        {actorDetails?.firstName && (
          <>
            <span>
              Name: {actorType === 'DOCTOR' ? 'Dr. ' : ''}
              {actorDetails.firstName || '-'} {actorDetails.lastName || '-'}
            </span>
          </>
        )}
      </div>
    </LableValue>
  );
};
