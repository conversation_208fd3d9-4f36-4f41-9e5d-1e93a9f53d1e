import type { CoreExtraContext } from '@segment/analytics-core';

// export const TRACKING_COOKIE_NAME = 'w_tracking';
// export const TRACKING_FIRST_TOUCH_COOKIE_NAME = 'w_ft_tracking';
export const TRACKING_AFFILIATE_COOKIE_NAME = 'w_affiliate';

export type CampaignData = CoreExtraContext['campaign'];

export function genCampaignDataFromParams(
  params: Record<string, string> = {},
): CampaignData {
  const data = {
    name: params.utm_campaign,
    source: params.utm_source,
    medium: params.utm_medium,
    term: params.utm_term,
    content: params.utm_content,
  };
  if (Object.values(data).every((v) => !v)) {
    return undefined;
  }
  return data as CampaignData;
}
